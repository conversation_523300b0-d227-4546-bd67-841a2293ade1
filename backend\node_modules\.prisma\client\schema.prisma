// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id         String   @id @default(cuid())
  email      String   @unique
  name       String?
  image      String?
  provider   String   @default("google")
  providerId String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  domains Domain[]

  @@map("users")
}

model Domain {
  id        String   @id @default(cuid())
  name      String
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  securityLogs SecurityLog[]
  attacks      Attack[]

  @@map("domains")
}

model SecurityLog {
  id        String   @id @default(cuid())
  domainId  String
  timestamp DateTime @default(now())
  message   String
  type      LogType  @default(NORMAL)
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  domain Domain @relation(fields: [domainId], references: [id], onDelete: Cascade)

  @@map("security_logs")
}

model Attack {
  id                String       @id @default(cuid())
  domainId          String
  type              AttackType
  status            AttackStatus @default(ACTIVE)
  severity          Severity     @default(MEDIUM)
  detectedAt        DateTime     @default(now())
  resolvedAt        DateTime?
  mitigationDetails String?
  ipAddress         String?
  userAgent         String?
  payload           String?
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @updatedAt

  domain Domain @relation(fields: [domainId], references: [id], onDelete: Cascade)

  @@map("attacks")
}

enum LogType {
  NORMAL
  WARNING
  ERROR
}

enum AttackType {
  DDOS
  SQL_INJECTION
  XSS
  CSRF
  BRUTE_FORCE
  MALWARE
  PHISHING
}

enum AttackStatus {
  ACTIVE
  INVESTIGATING
  RESOLVED
  FALSE_POSITIVE
}

enum Severity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}
