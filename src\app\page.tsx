'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import Logo from '@/components/Logo'
import Button from '@/components/Button'
import GoogleIcon from '@/components/GoogleIcon'

export default function LoginPage() {
  const router = useRouter()

  const handleGoogleLogin = () => {
    // Simulate login and redirect to dashboard
    router.push('/dashboard')
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="w-full max-w-md">
        <div className="bg-white rounded-lg shadow-lg p-8">
          {/* Logo and Title Section */}
          <div className="flex flex-col items-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Logo size="lg" />
              <h1 className="text-3xl font-bold text-gray-900">SYRA</h1>
            </div>
            <p className="text-lg text-gray-600 text-center">
              Secure Your Realm Always
            </p>
          </div>

          {/* Google Login Button */}
          <Button
            variant="secondary"
            size="lg"
            onClick={handleGoogleLogin}
            icon={<GoogleIcon />}
            className="w-full border-gray-300 hover:border-gray-400"
          >
            Sign in with Google
          </Button>
        </div>
      </div>
    </div>
  )
}
