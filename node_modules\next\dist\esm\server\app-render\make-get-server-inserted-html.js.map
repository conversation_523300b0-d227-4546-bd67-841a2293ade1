{"version": 3, "sources": ["../../../src/server/app-render/make-get-server-inserted-html.tsx"], "names": ["React", "isNotFoundError", "getURLFromRedirectError", "isRedirectError", "getRedirectStatusCodeFromError", "renderToReadableStream", "streamToString", "makeGetServerInsertedHTML", "polyfills", "renderServerInsertedHTML", "hasPostponed", "flushedErrorMetaTagsUntilIndex", "polyfillsFlushed", "getServerInsertedHTML", "serverCapturedErrors", "errorMetaTags", "length", "error", "push", "meta", "name", "content", "key", "digest", "process", "env", "NODE_ENV", "redirectUrl", "isPermanent", "httpEquiv", "stream", "map", "polyfill", "script", "src", "allReady"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,eAAe,QAAQ,oCAAmC;AACnE,SACEC,uBAAuB,EACvBC,eAAe,QACV,mCAAkC;AACzC,SAASC,8BAA8B,QAAQ,8DAA6D;AAC5G,SAASC,sBAAsB,QAAQ,wBAAuB;AAC9D,SAASC,cAAc,QAAQ,0CAAyC;AAExE,OAAO,SAASC,0BAA0B,EACxCC,SAAS,EACTC,wBAAwB,EACxBC,YAAY,EAKb;IACC,IAAIC,iCAAiC;IACrC,2EAA2E;IAC3E,IAAIC,mBAAmBF;IAEvB,OAAO,eAAeG,sBAAsBC,oBAA6B;QACvE,kEAAkE;QAClE,WAAW;QACX,MAAMC,gBAAgB,EAAE;QACxB,MAAOJ,iCAAiCG,qBAAqBE,MAAM,CAAE;YACnE,MAAMC,QAAQH,oBAAoB,CAACH,+BAA+B;YAClEA;YAEA,IAAIV,gBAAgBgB,QAAQ;gBAC1BF,cAAcG,IAAI,eAChB,oBAACC;oBAAKC,MAAK;oBAASC,SAAQ;oBAAUC,KAAKL,MAAMM,MAAM;oBACvDC,QAAQC,GAAG,CAACC,QAAQ,KAAK,8BACvB,oBAACP;oBAAKC,MAAK;oBAAaC,SAAQ;oBAAYC,KAAI;qBAC9C;YAER,OAAO,IAAInB,gBAAgBc,QAAQ;gBACjC,MAAMU,cAAczB,wBAAwBe;gBAC5C,MAAMW,cACJxB,+BAA+Ba,WAAW,MAAM,OAAO;gBACzD,IAAIU,aAAa;oBACfZ,cAAcG,IAAI,eAChB,oBAACC;wBACCU,WAAU;wBACVR,SAAS,CAAC,EAAEO,cAAc,IAAI,EAAE,KAAK,EAAED,YAAY,CAAC;wBACpDL,KAAKL,MAAMM,MAAM;;gBAGvB;YACF;QACF;QAEA,MAAMO,SAAS,MAAMzB,qCACnB,0CAEG,CAACO,qBACAJ,6BAAAA,UAAWuB,GAAG,CAAC,CAACC;YACd,qBAAO,oBAACC;gBAAOX,KAAKU,SAASE,GAAG;gBAAG,GAAGF,QAAQ;;QAChD,KACDvB,4BACAM;QAIL,6DAA6D;QAC7D,IAAI,CAACH,kBAAkBA,mBAAmB;QAE1C,mCAAmC;QACnC,MAAMkB,OAAOK,QAAQ;QAErB,OAAO7B,eAAewB;IACxB;AACF"}