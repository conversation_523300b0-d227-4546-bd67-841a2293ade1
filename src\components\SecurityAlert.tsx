import React from 'react'
import { AlertCircle } from 'lucide-react'

interface SecurityAlertProps {
  title: string
  description: string
  type?: 'error' | 'warning' | 'info'
}

export default function SecurityAlert({ title, description }: SecurityAlertProps) {
  return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-4 mt-4">
      <div className="flex items-start gap-3">
        <div className="w-4 h-4 bg-red-500 rounded-full flex-shrink-0 mt-1">
          <span className="sr-only">Error</span>
        </div>
        <div className="flex-1">
          <h5 className="text-sm font-semibold text-red-800 mb-2">{title}</h5>
          <p className="text-xs text-red-700 leading-relaxed">{description}</p>
        </div>
      </div>
    </div>
  )
}
