"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/DomainCard.tsx":
/*!***************************************!*\
  !*** ./src/components/DomainCard.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DomainCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Logo */ \"(app-pages-browser)/./src/components/Logo.tsx\");\n/* harmony import */ var _SecurityAlert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SecurityAlert */ \"(app-pages-browser)/./src/components/SecurityAlert.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction DomainCard(param) {\n    let { domain } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleClick = ()=>{\n        router.push(\"/domain/\".concat(domain.id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white border border-gray-200 rounded-lg p-6 cursor-pointer hover:shadow-lg transition-shadow\",\n        onClick: handleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        size: \"md\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: domain.name\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-gray-900 mb-3\",\n                        children: \"Security Log:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: domain.securityLogs.slice(0, 3).map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs \".concat(log.type === \"error\" ? \"text-red-500\" : \"text-gray-500\"),\n                                children: [\n                                    log.timestamp,\n                                    \" \",\n                                    log.message\n                                ]\n                            }, log.id, true, {\n                                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            domain.hasActiveAlert && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SecurityAlert__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                title: \"SQL Injection Detected\",\n                description: \"The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack\"\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainCard, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DomainCard;\nvar _c;\n$RefreshReg$(_c, \"DomainCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DomainCard.tsx\n"));

/***/ })

});