/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/domain/[id]/page";
exports.ids = ["app/domain/[id]/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdomain%2F%5Bid%5D%2Fpage&page=%2Fdomain%2F%5Bid%5D%2Fpage&appPaths=%2Fdomain%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fdomain%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdomain%2F%5Bid%5D%2Fpage&page=%2Fdomain%2F%5Bid%5D%2Fpage&appPaths=%2Fdomain%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fdomain%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'domain',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/domain/[id]/page.tsx */ \"(rsc)/./src/app/domain/[id]/page.tsx\")), \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/domain/[id]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/domain/[id]/page\",\n        pathname: \"/domain/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdomain%2F%5Bid%5D%2Fpage&page=%2Fdomain%2F%5Bid%5D%2Fpage&appPaths=%2Fdomain%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fdomain%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp%5Cdomain%5C%5Bid%5D%5Cpage.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp%5Cdomain%5C%5Bid%5D%5Cpage.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/domain/[id]/page.tsx */ \"(ssr)/./src/app/domain/[id]/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q1NFTUVTVEVSJTIwNiU1Q1Byb3llayUyMFV0YW1hJTIwSW5mb3JtYXRpa2ElNUNwdWklNUNzcmMlNUNhcHAlNUNkb21haW4lNUMlNUJpZCU1RCU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N5cmEtc2VjdXJpdHktZGFzaGJvYXJkLz9iYTY5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcU0VNRVNURVIgNlxcXFxQcm95ZWsgVXRhbWEgSW5mb3JtYXRpa2FcXFxccHVpXFxcXHNyY1xcXFxhcHBcXFxcZG9tYWluXFxcXFtpZF1cXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp%5Cdomain%5C%5Bid%5D%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/domain/[id]/page.tsx":
/*!**************************************!*\
  !*** ./src/app/domain/[id]/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DomainDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Navigation */ \"(ssr)/./src/components/Navigation.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Footer */ \"(ssr)/./src/components/Footer.tsx\");\n/* harmony import */ var _components_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Button */ \"(ssr)/./src/components/Button.tsx\");\n/* harmony import */ var _components_Logo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Logo */ \"(ssr)/./src/components/Logo.tsx\");\n/* harmony import */ var _components_AttackStatsCard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/AttackStatsCard */ \"(ssr)/./src/components/AttackStatsCard.tsx\");\n/* harmony import */ var _components_AttackDetailCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/AttackDetailCard */ \"(ssr)/./src/components/AttackDetailCard.tsx\");\n/* harmony import */ var _lib_mockData__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/mockData */ \"(ssr)/./src/lib/mockData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction DomainDetailPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const domainId = params.id;\n    const domain = _lib_mockData__WEBPACK_IMPORTED_MODULE_9__.mockDomains.find((d)=>d.id === domainId);\n    const handleLogout = ()=>{\n        router.push(\"/\");\n    };\n    const handleBack = ()=>{\n        router.push(\"/dashboard\");\n    };\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                        children: \"Domain not found\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        onClick: handleBack,\n                        children: \"Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this);\n    }\n    // Mock additional security logs for detail view\n    const extendedLogs = [\n        ...domain.securityLogs,\n        {\n            id: \"4\",\n            timestamp: \"[2025-05-1 10:22:47]\",\n            message: \"No threats detected – normal activity from all sources\",\n            type: \"normal\"\n        },\n        {\n            id: \"5\",\n            timestamp: \"[2025-05-1 10:22:46]\",\n            message: \"No threats detected – normal activity from all sources\",\n            type: \"normal\"\n        },\n        {\n            id: \"6\",\n            timestamp: \"[2025-05-1 10:22:43]\",\n            message: \"Clean traffic observed – no suspicious patterns found\",\n            type: \"normal\"\n        },\n        {\n            id: \"7\",\n            timestamp: \"[2025-05-1 10:22:42]\",\n            message: \"Clean traffic observed – no suspicious patterns found\",\n            type: \"normal\"\n        },\n        {\n            id: \"8\",\n            timestamp: \"[2025-05-1 10:22:41]\",\n            message: \"Clean traffic observed – no suspicious patterns found\",\n            type: \"normal\"\n        },\n        {\n            id: \"9\",\n            timestamp: \"[2025-05-1 10:22:40]\",\n            message: \"Clean traffic observed – no suspicious patterns found\",\n            type: \"normal\"\n        },\n        {\n            id: \"10\",\n            timestamp: \"[2025-05-1 10:22:39]\",\n            message: \"Clean traffic observed – no suspicious patterns found\",\n            type: \"normal\"\n        },\n        {\n            id: \"11\",\n            timestamp: \"[2025-05-1 10:22:38]\",\n            message: \"Clean traffic observed – no suspicious patterns found\",\n            type: \"normal\"\n        },\n        {\n            id: \"12\",\n            timestamp: \"[2025-05-1 10:22:37]\",\n            message: \"Clean traffic observed – no suspicious patterns found\",\n            type: \"normal\"\n        }\n    ];\n    // Mock attack details for demonstration\n    const mockAttacks = [\n        {\n            id: \"1\",\n            type: \"DDoS\",\n            status: \"Resolved\",\n            severity: \"Medium\",\n            detectedAt: \"2025-05-1 10:22:45\",\n            resolvedAt: \"2025-05-1 10:23:30\",\n            mitigationDetails: \"The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack\"\n        },\n        {\n            id: \"2\",\n            type: \"SQL Injection\",\n            status: \"Resolved\",\n            severity: \"Medium\",\n            detectedAt: \"2025-05-1 10:22:45\",\n            resolvedAt: \"2025-05-1 10:23:30\",\n            mitigationDetails: \"The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack\"\n        },\n        {\n            id: \"3\",\n            type: \"XSS\",\n            status: \"Resolved\",\n            severity: \"Medium\",\n            detectedAt: \"2025-05-1 10:22:45\",\n            resolvedAt: \"2025-05-1 10:23:30\",\n            mitigationDetails: \"The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onLogout: handleLogout\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 px-20 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"text\",\n                            size: \"lg\",\n                            onClick: handleBack,\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, void 0, void 0),\n                            className: \"w-31\",\n                            children: \"Kembali\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card flex flex-col items-center gap-6 py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Logo__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: \"lg\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-base text-gray-400\",\n                                            children: [\n                                                \"Created At: \",\n                                                domain.createdAt,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 47\n                                                }, this),\n                                                \"Last Update At: \",\n                                                domain.lastUpdate\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4 w-full max-w-2xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AttackStatsCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            count: domain.attackCounts.ddos,\n                                            type: \"DDoS\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AttackStatsCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            count: domain.attackCounts.sqlInjection,\n                                            type: \"SQL Injection\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AttackStatsCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            count: domain.attackCounts.xss,\n                                            type: \"XSS\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                children: \"Security Log\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 space-y-1\",\n                                            children: extendedLogs.map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: `text-base ${log.type === \"error\" ? \"text-red-500\" : \"text-gray-400\"}`,\n                                                    children: [\n                                                        log.timestamp,\n                                                        \" \",\n                                                        log.message\n                                                    ]\n                                                }, log.id, true, {\n                                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 bg-gray-100 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                children: \"Detected Attacks\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: mockAttacks.map((attack)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AttackDetailCard__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        attack: attack\n                                    }, attack.id, false, {\n                                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\domain\\\\[id]\\\\page.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/domain/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AttackDetailCard.tsx":
/*!*********************************************!*\
  !*** ./src/components/AttackDetailCard.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AttackDetailCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction AttackDetailCard({ attack }) {\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"Resolved\":\n                return \"text-green-600\";\n            case \"Active\":\n                return \"text-red-600\";\n            case \"Investigating\":\n                return \"text-yellow-600\";\n            default:\n                return \"text-gray-600\";\n        }\n    };\n    const getSeverityColor = (severity)=>{\n        switch(severity){\n            case \"Critical\":\n                return \"text-red-600\";\n            case \"High\":\n                return \"text-orange-600\";\n            case \"Medium\":\n                return \"text-yellow-600\";\n            case \"Low\":\n                return \"text-green-600\";\n            default:\n                return \"text-gray-600\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-background-secondary rounded p-3 flex-1\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-base font-semibold text-gray-900\",\n                    children: [\n                        attack.type,\n                        \" Detected\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-base text-gray-400\",\n                                    children: \"Mitigation Status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `text-base font-medium ${getStatusColor(attack.status)}`,\n                                    children: attack.status\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-base text-gray-400\",\n                                    children: \"Severity\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `text-base font-medium ${getSeverityColor(attack.severity)}`,\n                                    children: attack.severity\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-base text-gray-400\",\n                                    children: \"Detected At\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-base font-medium text-gray-900\",\n                                    children: attack.detectedAt\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        attack.resolvedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-base text-gray-400\",\n                                    children: \"Resolved At\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-base font-medium text-gray-900\",\n                                    children: attack.resolvedAt\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-base text-gray-400\",\n                                    children: \"Mitigation Details\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-100 rounded p-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base text-gray-900\",\n                                        children: attack.mitigationDetails\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackDetailCard.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AttackDetailCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AttackStatsCard.tsx":
/*!********************************************!*\
  !*** ./src/components/AttackStatsCard.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AttackStatsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction AttackStatsCard({ count, type }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-8 flex flex-col items-center gap-3 flex-1 min-h-[120px] justify-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-4xl font-bold text-gray-900\",\n                children: count\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackStatsCard.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm text-gray-500 text-center font-medium\",\n                children: type\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackStatsCard.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackStatsCard.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9BdHRhY2tTdGF0c0NhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF5QjtBQU9WLFNBQVNDLGdCQUFnQixFQUFFQyxLQUFLLEVBQUVDLElBQUksRUFBd0I7SUFDM0UscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBS0QsV0FBVTswQkFBb0NIOzs7Ozs7MEJBQ3BELDhEQUFDSTtnQkFBS0QsV0FBVTswQkFBaURGOzs7Ozs7Ozs7Ozs7QUFHdkUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zeXJhLXNlY3VyaXR5LWRhc2hib2FyZC8uL3NyYy9jb21wb25lbnRzL0F0dGFja1N0YXRzQ2FyZC50c3g/OWM5OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBBdHRhY2tTdGF0c0NhcmRQcm9wcyB7XG4gIGNvdW50OiBudW1iZXJcbiAgdHlwZTogc3RyaW5nXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEF0dGFja1N0YXRzQ2FyZCh7IGNvdW50LCB0eXBlIH06IEF0dGFja1N0YXRzQ2FyZFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHJvdW5kZWQtbGcgcC04IGZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGdhcC0zIGZsZXgtMSBtaW4taC1bMTIwcHhdIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPntjb3VudH08L3NwYW4+XG4gICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgdGV4dC1jZW50ZXIgZm9udC1tZWRpdW1cIj57dHlwZX08L3NwYW4+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkF0dGFja1N0YXRzQ2FyZCIsImNvdW50IiwidHlwZSIsImRpdiIsImNsYXNzTmFtZSIsInNwYW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AttackStatsCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Button.tsx":
/*!***********************************!*\
  !*** ./src/components/Button.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n\n\n\nfunction Button({ variant = \"primary\", size = \"md\", children, icon, iconPosition = \"left\", className, ...props }) {\n    const baseClasses = \"inline-flex items-center justify-center font-medium rounded transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2\";\n    const variantClasses = {\n        primary: \"bg-primary text-white hover:bg-primary-600 focus:ring-primary shadow-button\",\n        secondary: \"bg-white text-gray-900 border border-gray-300 hover:bg-gray-50 focus:ring-gray-500\",\n        text: \"text-gray-900 hover:bg-gray-100 focus:ring-gray-500\",\n        danger: \"bg-red-500 text-white hover:bg-red-600 focus:ring-red-500\"\n    };\n    const sizeClasses = {\n        sm: \"px-3 py-1.5 text-sm h-8\",\n        md: \"px-4 py-2 text-base h-10\",\n        lg: \"px-6 py-2.5 text-base h-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(baseClasses, variantClasses[variant], sizeClasses[size], className),\n        ...props,\n        children: [\n            icon && iconPosition === \"left\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this),\n            children,\n            icon && iconPosition === \"right\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Button.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-white border-t border-gray-200 py-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Copyright 2025 | Nyoman Darmayoga - PUI\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 8,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Footer.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Gb290ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF5QjtBQUdWLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFPQyxXQUFVO2tCQUNoQiw0RUFBQ0M7WUFBSUQsV0FBVTtzQkFDYiw0RUFBQ0M7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNFO29CQUFFRixXQUFVOzhCQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTy9DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3lyYS1zZWN1cml0eS1kYXNoYm9hcmQvLi9zcmMvY29tcG9uZW50cy9Gb290ZXIudHN4PzM1MWYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgQ29weXJpZ2h0IH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBGb290ZXIoKSB7XG4gIHJldHVybiAoXG4gICAgPGZvb3RlciBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXItdCBib3JkZXItZ3JheS0yMDAgcHktNlwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgIENvcHlyaWdodCAyMDI1IHwgTnlvbWFuIERhcm1heW9nYSAtIFBVSVxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Zvb3Rlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiRm9vdGVyIiwiZm9vdGVyIiwiY2xhc3NOYW1lIiwiZGl2IiwicCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Logo.tsx":
/*!*********************************!*\
  !*** ./src/components/Logo.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Logo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction Logo({ size = \"md\", className = \"\" }) {\n    const sizeClasses = {\n        sm: \"w-8 h-8\",\n        md: \"w-10 h-10\",\n        lg: \"w-20 h-20\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${sizeClasses[size]} ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n            src: \"/syra-logo.png\",\n            alt: \"SYRA Logo\",\n            width: size === \"sm\" ? 32 : size === \"md\" ? 40 : 80,\n            height: size === \"sm\" ? 32 : size === \"md\" ? 40 : 80,\n            className: \"w-full h-full object-contain\"\n        }, void 0, false, {\n            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Logo.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Logo.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Mb2dvLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF5QjtBQUVLO0FBT2YsU0FBU0UsS0FBSyxFQUFFQyxPQUFPLElBQUksRUFBRUMsWUFBWSxFQUFFLEVBQWE7SUFDckUsTUFBTUMsY0FBYztRQUNsQkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7SUFDTjtJQUVBLHFCQUNFLDhEQUFDQztRQUFJTCxXQUFXLENBQUMsRUFBRUMsV0FBVyxDQUFDRixLQUFLLENBQUMsQ0FBQyxFQUFFQyxVQUFVLENBQUM7a0JBQ2pELDRFQUFDSCxtREFBS0E7WUFDSlMsS0FBSTtZQUNKQyxLQUFJO1lBQ0pDLE9BQU9ULFNBQVMsT0FBTyxLQUFLQSxTQUFTLE9BQU8sS0FBSztZQUNqRFUsUUFBUVYsU0FBUyxPQUFPLEtBQUtBLFNBQVMsT0FBTyxLQUFLO1lBQ2xEQyxXQUFVOzs7Ozs7Ozs7OztBQUlsQiIsInNvdXJjZXMiOlsid2VicGFjazovL3N5cmEtc2VjdXJpdHktZGFzaGJvYXJkLy4vc3JjL2NvbXBvbmVudHMvTG9nby50c3g/Njc1YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmltcG9ydCBJbWFnZSBmcm9tICduZXh0L2ltYWdlJ1xuXG5pbnRlcmZhY2UgTG9nb1Byb3BzIHtcbiAgc2l6ZT86ICdzbScgfCAnbWQnIHwgJ2xnJ1xuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9nbyh7IHNpemUgPSAnbWQnLCBjbGFzc05hbWUgPSAnJyB9OiBMb2dvUHJvcHMpIHtcbiAgY29uc3Qgc2l6ZUNsYXNzZXMgPSB7XG4gICAgc206ICd3LTggaC04JyxcbiAgICBtZDogJ3ctMTAgaC0xMCcsXG4gICAgbGc6ICd3LTIwIGgtMjAnXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtzaXplQ2xhc3Nlc1tzaXplXX0gJHtjbGFzc05hbWV9YH0+XG4gICAgICA8SW1hZ2VcbiAgICAgICAgc3JjPVwiL3N5cmEtbG9nby5wbmdcIlxuICAgICAgICBhbHQ9XCJTWVJBIExvZ29cIlxuICAgICAgICB3aWR0aD17c2l6ZSA9PT0gJ3NtJyA/IDMyIDogc2l6ZSA9PT0gJ21kJyA/IDQwIDogODB9XG4gICAgICAgIGhlaWdodD17c2l6ZSA9PT0gJ3NtJyA/IDMyIDogc2l6ZSA9PT0gJ21kJyA/IDQwIDogODB9XG4gICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvbnRhaW5cIlxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiSW1hZ2UiLCJMb2dvIiwic2l6ZSIsImNsYXNzTmFtZSIsInNpemVDbGFzc2VzIiwic20iLCJtZCIsImxnIiwiZGl2Iiwic3JjIiwiYWx0Iiwid2lkdGgiLCJoZWlnaHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Logo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Logo */ \"(ssr)/./src/components/Logo.tsx\");\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Button */ \"(ssr)/./src/components/Button.tsx\");\n\n\n\n\nfunction Navigation({ userName = \"I Nyoman Darmayoga\", onLogout }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-sm border-b border-gray-200 px-6 py-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            size: \"lg\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"SYRA\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-semibold text-sm\",\n                                        children: \"I\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-900\",\n                                    children: userName\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            variant: \"primary\",\n                            size: \"sm\",\n                            onClick: onLogout,\n                            children: \"Log Out\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/mockData.ts":
/*!*****************************!*\
  !*** ./src/lib/mockData.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTotalAttacks: () => (/* binding */ getTotalAttacks),\n/* harmony export */   getTotalAttacksByType: () => (/* binding */ getTotalAttacksByType),\n/* harmony export */   mockDomains: () => (/* binding */ mockDomains)\n/* harmony export */ });\nconst mockDomains = [\n    {\n        id: \"1\",\n        name: \"trenteknologimobile.com\",\n        createdAt: \"1 May 2025\",\n        lastUpdate: \"2 May 2025\",\n        attackCounts: {\n            ddos: 0,\n            sqlInjection: 1,\n            xss: 0\n        },\n        hasActiveAlert: true,\n        securityLogs: [\n            {\n                id: \"1\",\n                timestamp: \"[2025-05-1 10:22:45]\",\n                message: \"SQL detected from 192.168.1.77 on /login.php?user=adm4'-- (status: 403)\",\n                type: \"error\"\n            },\n            {\n                id: \"2\",\n                timestamp: \"[2025-05-1 10:22:46]\",\n                message: \"No threats detected – normal activity from all sources\",\n                type: \"normal\"\n            },\n            {\n                id: \"3\",\n                timestamp: \"[2025-05-1 10:22:43]\",\n                message: \"Clean traffic observed – no suspicious patterns found\",\n                type: \"normal\"\n            }\n        ],\n        attacks: [\n            {\n                id: \"1\",\n                type: \"SQL Injection\",\n                status: \"Resolved\",\n                severity: \"Medium\",\n                detectedAt: \"2025-05-1 10:22:45\",\n                resolvedAt: \"2025-05-1 10:23:30\",\n                mitigationDetails: \"The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack\"\n            }\n        ]\n    },\n    {\n        id: \"2\",\n        name: \"proyekutamainformatika.com\",\n        createdAt: \"1 May 2025\",\n        lastUpdate: \"2 May 2025\",\n        attackCounts: {\n            ddos: 0,\n            sqlInjection: 0,\n            xss: 0\n        },\n        hasActiveAlert: false,\n        securityLogs: [\n            {\n                id: \"1\",\n                timestamp: \"[2025-05-1 10:22:47]\",\n                message: \"No threats detected – normal activity from all sources\",\n                type: \"normal\"\n            },\n            {\n                id: \"2\",\n                timestamp: \"[2025-05-10 10:22:46]\",\n                message: \"No threats detected – normal activity from all sources\",\n                type: \"normal\"\n            },\n            {\n                id: \"3\",\n                timestamp: \"[2025-05-1 10:22:43]\",\n                message: \"Clean traffic observed – no suspicious patterns found\",\n                type: \"normal\"\n            }\n        ],\n        attacks: []\n    },\n    {\n        id: \"3\",\n        name: \"websiteku.org\",\n        createdAt: \"1 May 2025\",\n        lastUpdate: \"2 May 2025\",\n        attackCounts: {\n            ddos: 0,\n            sqlInjection: 0,\n            xss: 0\n        },\n        hasActiveAlert: false,\n        securityLogs: [\n            {\n                id: \"1\",\n                timestamp: \"[2025-05-1 10:22:47]\",\n                message: \"No threats detected – normal activity from all sources\",\n                type: \"normal\"\n            },\n            {\n                id: \"2\",\n                timestamp: \"[2025-05-10 10:22:46]\",\n                message: \"No threats detected – normal activity from all sources\",\n                type: \"normal\"\n            },\n            {\n                id: \"3\",\n                timestamp: \"[2025-05-1 10:22:43]\",\n                message: \"Clean traffic observed – no suspicious patterns found\",\n                type: \"normal\"\n            }\n        ],\n        attacks: []\n    }\n];\nconst getTotalAttacks = ()=>{\n    return mockDomains.reduce((total, domain)=>{\n        return total + domain.attackCounts.ddos + domain.attackCounts.sqlInjection + domain.attackCounts.xss;\n    }, 0);\n};\nconst getTotalAttacksByType = ()=>{\n    return mockDomains.reduce((totals, domain)=>{\n        totals.ddos += domain.attackCounts.ddos;\n        totals.sqlInjection += domain.attackCounts.sqlInjection;\n        totals.xss += domain.attackCounts.xss;\n        return totals;\n    }, {\n        ddos: 0,\n        sqlInjection: 0,\n        xss: 0\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/mockData.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"466fcb249153\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3lyYS1zZWN1cml0eS1kYXNoYm9hcmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzUyYjAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0NjZmY2IyNDkxNTNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/domain/[id]/page.tsx":
/*!**************************************!*\
  !*** ./src/app/domain/[id]/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\SEMESTER 6\Proyek Utama Informatika\pui\src\app\domain\[id]\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"SYRA - Secure Your Realm Always\",\n    description: \"Security monitoring dashboard for web applications\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7c0JBQU1IOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3lyYS1zZWN1cml0eS1kYXNoYm9hcmQvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdTWVJBIC0gU2VjdXJlIFlvdXIgUmVhbG0gQWx3YXlzJyxcbiAgZGVzY3JpcHRpb246ICdTZWN1cml0eSBtb25pdG9yaW5nIGRhc2hib2FyZCBmb3Igd2ViIGFwcGxpY2F0aW9ucycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHk+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdomain%2F%5Bid%5D%2Fpage&page=%2Fdomain%2F%5Bid%5D%2Fpage&appPaths=%2Fdomain%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fdomain%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();