/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\")), \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp%5Cdashboard%5Cpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp%5Cdashboard%5Cpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q1NFTUVTVEVSJTIwNiU1Q1Byb3llayUyMFV0YW1hJTIwSW5mb3JtYXRpa2ElNUNwdWklNUNzcmMlNUNhcHAlNUNkYXNoYm9hcmQlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zeXJhLXNlY3VyaXR5LWRhc2hib2FyZC8/ODAyZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFNFTUVTVEVSIDZcXFxcUHJveWVrIFV0YW1hIEluZm9ybWF0aWthXFxcXHB1aVxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp%5Cdashboard%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Navigation */ \"(ssr)/./src/components/Navigation.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Footer */ \"(ssr)/./src/components/Footer.tsx\");\n/* harmony import */ var _components_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Button */ \"(ssr)/./src/components/Button.tsx\");\n/* harmony import */ var _components_AttackStatsCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/AttackStatsCard */ \"(ssr)/./src/components/AttackStatsCard.tsx\");\n/* harmony import */ var _components_DomainCard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/DomainCard */ \"(ssr)/./src/components/DomainCard.tsx\");\n/* harmony import */ var _components_AddDomainModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/AddDomainModal */ \"(ssr)/./src/components/AddDomainModal.tsx\");\n/* harmony import */ var _lib_mockData__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/mockData */ \"(ssr)/./src/lib/mockData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [domains, setDomains] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_mockData__WEBPACK_IMPORTED_MODULE_9__.mockDomains);\n    const attackStats = (0,_lib_mockData__WEBPACK_IMPORTED_MODULE_9__.getTotalAttacksByType)();\n    const handleLogout = ()=>{\n        router.push(\"/\");\n    };\n    const handleAddDomain = (domainName)=>{\n        const newDomain = {\n            id: String(domains.length + 1),\n            name: domainName,\n            createdAt: new Date().toLocaleDateString(\"en-GB\", {\n                day: \"numeric\",\n                month: \"long\",\n                year: \"numeric\"\n            }),\n            lastUpdate: new Date().toLocaleDateString(\"en-GB\", {\n                day: \"numeric\",\n                month: \"long\",\n                year: \"numeric\"\n            }),\n            attackCounts: {\n                ddos: 0,\n                sqlInjection: 0,\n                xss: 0\n            },\n            securityLogs: [\n                {\n                    id: \"1\",\n                    timestamp: \"[\" + new Date().toISOString().slice(0, 16).replace(\"T\", \" \") + \"]\",\n                    message: \"Domain monitoring started - no threats detected\",\n                    type: \"normal\"\n                }\n            ],\n            attacks: [],\n            hasActiveAlert: false\n        };\n        setDomains([\n            ...domains,\n            newDomain\n        ]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onLogout: handleLogout\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 px-20 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"primary\",\n                            size: \"lg\",\n                            onClick: ()=>setIsModalOpen(true),\n                            className: \"w-50\",\n                            children: \"Add New Domain\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                children: \"Total Number of Attacks Detected\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AttackStatsCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        count: attackStats.ddos,\n                                        type: \"DDoS\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AttackStatsCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        count: attackStats.sqlInjection,\n                                        type: \"SQL Injection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AttackStatsCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        count: attackStats.xss,\n                                        type: \"XSS\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                children: \"My Domain\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                children: domains.map((domain)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DomainCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        domain: domain\n                                    }, domain.id, false, {\n                                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AddDomainModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: isModalOpen,\n                onClose: ()=>setIsModalOpen(false),\n                onSave: handleAddDomain\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AddDomainModal.tsx":
/*!*******************************************!*\
  !*** ./src/components/AddDomainModal.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddDomainModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Button */ \"(ssr)/./src/components/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AddDomainModal({ isOpen, onClose, onSave }) {\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSave = ()=>{\n        if (domain.trim()) {\n            onSave(domain.trim());\n            setDomain(\"\");\n            onClose();\n        }\n    };\n    const handleCancel = ()=>{\n        setDomain(\"\");\n        onClose();\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-background-overlay flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded p-6 w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Add New Domain\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-xs text-gray-900 mb-2\",\n                            children: \"Domain Link\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: domain,\n                            onChange: (e)=>setDomain(e.target.value),\n                            placeholder: \"E.g. trenteknologimobile.com\",\n                            className: \"input\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            variant: \"secondary\",\n                            size: \"lg\",\n                            onClick: handleCancel,\n                            className: \"flex-1\",\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            variant: \"primary\",\n                            size: \"lg\",\n                            onClick: handleSave,\n                            className: \"flex-1\",\n                            children: \"Save\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AddDomainModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AttackStatsCard.tsx":
/*!********************************************!*\
  !*** ./src/components/AttackStatsCard.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AttackStatsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction AttackStatsCard({ count, type }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-background-secondary rounded p-5 flex flex-col items-center gap-2 flex-1\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-2xl font-semibold text-gray-900\",\n                children: count\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackStatsCard.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-base text-gray-400 text-center\",\n                children: type\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackStatsCard.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackStatsCard.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9BdHRhY2tTdGF0c0NhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF5QjtBQU9WLFNBQVNDLGdCQUFnQixFQUFFQyxLQUFLLEVBQUVDLElBQUksRUFBd0I7SUFDM0UscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBS0QsV0FBVTswQkFBd0NIOzs7Ozs7MEJBQ3hELDhEQUFDSTtnQkFBS0QsV0FBVTswQkFBdUNGOzs7Ozs7Ozs7Ozs7QUFHN0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zeXJhLXNlY3VyaXR5LWRhc2hib2FyZC8uL3NyYy9jb21wb25lbnRzL0F0dGFja1N0YXRzQ2FyZC50c3g/OWM5OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBBdHRhY2tTdGF0c0NhcmRQcm9wcyB7XG4gIGNvdW50OiBudW1iZXJcbiAgdHlwZTogc3RyaW5nXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEF0dGFja1N0YXRzQ2FyZCh7IGNvdW50LCB0eXBlIH06IEF0dGFja1N0YXRzQ2FyZFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy1iYWNrZ3JvdW5kLXNlY29uZGFyeSByb3VuZGVkIHAtNSBmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBnYXAtMiBmbGV4LTFcIj5cbiAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPntjb3VudH08L3NwYW4+XG4gICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgdGV4dC1ncmF5LTQwMCB0ZXh0LWNlbnRlclwiPnt0eXBlfTwvc3Bhbj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQXR0YWNrU3RhdHNDYXJkIiwiY291bnQiLCJ0eXBlIiwiZGl2IiwiY2xhc3NOYW1lIiwic3BhbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AttackStatsCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Button.tsx":
/*!***********************************!*\
  !*** ./src/components/Button.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n\n\n\nfunction Button({ variant = \"primary\", size = \"md\", children, icon, iconPosition = \"left\", className, ...props }) {\n    const baseClasses = \"inline-flex items-center justify-center font-medium rounded transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2\";\n    const variantClasses = {\n        primary: \"bg-primary text-white hover:bg-primary-600 focus:ring-primary shadow-button\",\n        secondary: \"bg-white text-gray-900 border border-gray-300 hover:bg-gray-50 focus:ring-gray-500\",\n        text: \"text-gray-900 hover:bg-gray-100 focus:ring-gray-500\",\n        danger: \"bg-red-500 text-white hover:bg-red-600 focus:ring-red-500\"\n    };\n    const sizeClasses = {\n        sm: \"px-3 py-1.5 text-sm h-8\",\n        md: \"px-4 py-2 text-base h-10\",\n        lg: \"px-6 py-2.5 text-base h-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(baseClasses, variantClasses[variant], sizeClasses[size], className),\n        ...props,\n        children: [\n            icon && iconPosition === \"left\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this),\n            children,\n            icon && iconPosition === \"right\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Button.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/DomainCard.tsx":
/*!***************************************!*\
  !*** ./src/components/DomainCard.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DomainCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Logo */ \"(ssr)/./src/components/Logo.tsx\");\n/* harmony import */ var _SecurityAlert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SecurityAlert */ \"(ssr)/./src/components/SecurityAlert.tsx\");\n\n\n\n\n\nfunction DomainCard({ domain }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleClick = ()=>{\n        router.push(`/domain/${domain.id}`);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card cursor-pointer hover:shadow-lg transition-shadow\",\n        onClick: handleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3 mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        size: \"sm\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg font-medium text-gray-900\",\n                        children: domain.name\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                className: \"border-gray-100 mb-3\"\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-base text-gray-900 mb-3\",\n                        children: \"Security Log:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1 opacity-80\",\n                        children: domain.securityLogs.slice(0, 3).map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-base ${log.type === \"error\" ? \"text-red-500\" : \"text-gray-400\"}`,\n                                children: [\n                                    log.timestamp,\n                                    \" \",\n                                    log.message\n                                ]\n                            }, log.id, true, {\n                                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            domain.hasActiveAlert && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SecurityAlert__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                title: \"SQL Injection Detected\",\n                description: \"The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack\"\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                lineNumber: 48,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/DomainCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Copyright_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Copyright!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copyright.js\");\n\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-white shadow-card border-t border-gray-100 px-10 py-3\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copyright_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-6 h-6 text-gray-900\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs text-gray-900\",\n                    children: \"Copyright 2025 I Nyoman Darmayoga - PUI\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Footer.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Gb290ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBeUI7QUFDZTtBQUV6QixTQUFTRTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBT0MsV0FBVTtrQkFDaEIsNEVBQUNDO1lBQUlELFdBQVU7OzhCQUNiLDhEQUFDSCxxRkFBU0E7b0JBQUNHLFdBQVU7Ozs7Ozs4QkFDckIsOERBQUNFO29CQUFLRixXQUFVOzhCQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNaEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zeXJhLXNlY3VyaXR5LWRhc2hib2FyZC8uL3NyYy9jb21wb25lbnRzL0Zvb3Rlci50c3g/MzUxZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBDb3B5cmlnaHQgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEZvb3RlcigpIHtcbiAgcmV0dXJuIChcbiAgICA8Zm9vdGVyIGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdy1jYXJkIGJvcmRlci10IGJvcmRlci1ncmF5LTEwMCBweC0xMCBweS0zXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0zXCI+XG4gICAgICAgIDxDb3B5cmlnaHQgY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LWdyYXktOTAwXCIgLz5cbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgQ29weXJpZ2h0IDIwMjUgSSBOeW9tYW4gRGFybWF5b2dhIC0gUFVJXG4gICAgICAgIDwvc3Bhbj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZm9vdGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDb3B5cmlnaHQiLCJGb290ZXIiLCJmb290ZXIiLCJjbGFzc05hbWUiLCJkaXYiLCJzcGFuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Logo.tsx":
/*!*********************************!*\
  !*** ./src/components/Logo.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Logo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Logo({ size = \"md\", className = \"\" }) {\n    const sizeClasses = {\n        sm: \"w-8 h-8\",\n        md: \"w-10 h-10\",\n        lg: \"w-20 h-20\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${sizeClasses[size]} ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-full bg-gradient-to-br from-primary to-purple-500 rounded shadow-logo flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-white font-semibold text-lg\",\n                children: \"S\"\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Logo.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Logo.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Mb2dvLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBeUI7QUFPVixTQUFTQyxLQUFLLEVBQUVDLE9BQU8sSUFBSSxFQUFFQyxZQUFZLEVBQUUsRUFBYTtJQUNyRSxNQUFNQyxjQUFjO1FBQ2xCQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtJQUNOO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlMLFdBQVcsQ0FBQyxFQUFFQyxXQUFXLENBQUNGLEtBQUssQ0FBQyxDQUFDLEVBQUVDLFVBQVUsQ0FBQztrQkFDakQsNEVBQUNLO1lBQUlMLFdBQVU7c0JBQ2IsNEVBQUNNO2dCQUFLTixXQUFVOzBCQUFtQzs7Ozs7Ozs7Ozs7Ozs7OztBQUkzRCIsInNvdXJjZXMiOlsid2VicGFjazovL3N5cmEtc2VjdXJpdHktZGFzaGJvYXJkLy4vc3JjL2NvbXBvbmVudHMvTG9nby50c3g/Njc1YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBMb2dvUHJvcHMge1xuICBzaXplPzogJ3NtJyB8ICdtZCcgfCAnbGcnXG4gIGNsYXNzTmFtZT86IHN0cmluZ1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2dvKHsgc2l6ZSA9ICdtZCcsIGNsYXNzTmFtZSA9ICcnIH06IExvZ29Qcm9wcykge1xuICBjb25zdCBzaXplQ2xhc3NlcyA9IHtcbiAgICBzbTogJ3ctOCBoLTgnLFxuICAgIG1kOiAndy0xMCBoLTEwJyxcbiAgICBsZzogJ3ctMjAgaC0yMCdcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2Ake3NpemVDbGFzc2VzW3NpemVdfSAke2NsYXNzTmFtZX1gfT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBiZy1ncmFkaWVudC10by1iciBmcm9tLXByaW1hcnkgdG8tcHVycGxlLTUwMCByb3VuZGVkIHNoYWRvdy1sb2dvIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCB0ZXh0LWxnXCI+Uzwvc3Bhbj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJMb2dvIiwic2l6ZSIsImNsYXNzTmFtZSIsInNpemVDbGFzc2VzIiwic20iLCJtZCIsImxnIiwiZGl2Iiwic3BhbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Logo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Logo */ \"(ssr)/./src/components/Logo.tsx\");\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Button */ \"(ssr)/./src/components/Button.tsx\");\n\n\n\n\nfunction Navigation({ userName = \"I Nyoman Darmayoga\", onLogout }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-card border-b border-gray-100 px-10 py-3\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            size: \"md\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-semibold text-gray-900\",\n                            children: \"SYRA\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-semibold text-sm\",\n                                        children: \"I\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-base font-semibold text-gray-900\",\n                                    children: userName\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            variant: \"primary\",\n                            size: \"sm\",\n                            onClick: onLogout,\n                            children: \"Log Out\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SecurityAlert.tsx":
/*!******************************************!*\
  !*** ./src/components/SecurityAlert.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SecurityAlert)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n\n\n\nfunction SecurityAlert({ title, description, type = \"error\" }) {\n    const bgColor = type === \"error\" ? \"bg-red-50\" : type === \"warning\" ? \"bg-yellow-50\" : \"bg-blue-50\";\n    const iconColor = type === \"error\" ? \"text-red-600\" : type === \"warning\" ? \"text-yellow-600\" : \"text-blue-600\";\n    const textColor = \"text-gray-900\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${bgColor} rounded p-3 flex gap-2`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: `w-6 h-6 ${iconColor} flex-shrink-0 mt-0.5`\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\SecurityAlert.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: `font-semibold text-xs leading-6 ${textColor}`,\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\SecurityAlert.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: `text-xs leading-5 ${textColor} mt-1`,\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\SecurityAlert.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\SecurityAlert.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\SecurityAlert.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SecurityAlert.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/mockData.ts":
/*!*****************************!*\
  !*** ./src/lib/mockData.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTotalAttacks: () => (/* binding */ getTotalAttacks),\n/* harmony export */   getTotalAttacksByType: () => (/* binding */ getTotalAttacksByType),\n/* harmony export */   mockDomains: () => (/* binding */ mockDomains)\n/* harmony export */ });\nconst mockDomains = [\n    {\n        id: \"1\",\n        name: \"trenteknologimobile.com\",\n        createdAt: \"1 May 2025\",\n        lastUpdate: \"2 May 2025\",\n        attackCounts: {\n            ddos: 0,\n            sqlInjection: 1,\n            xss: 0\n        },\n        hasActiveAlert: true,\n        securityLogs: [\n            {\n                id: \"1\",\n                timestamp: \"[2025-05-1 10:22:45]\",\n                message: \"SQL detected from 192.168.1.77 on /login.php?user=adm4'-- (status: 403)\",\n                type: \"error\"\n            },\n            {\n                id: \"2\",\n                timestamp: \"[2025-05-1 10:22:46]\",\n                message: \"No threats detected – normal activity from all sources\",\n                type: \"normal\"\n            },\n            {\n                id: \"3\",\n                timestamp: \"[2025-05-1 10:22:43]\",\n                message: \"Clean traffic observed – no suspicious patterns found\",\n                type: \"normal\"\n            }\n        ],\n        attacks: [\n            {\n                id: \"1\",\n                type: \"SQL Injection\",\n                status: \"Resolved\",\n                severity: \"Medium\",\n                detectedAt: \"2025-05-1 10:22:45\",\n                resolvedAt: \"2025-05-1 10:23:30\",\n                mitigationDetails: \"The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack\"\n            }\n        ]\n    },\n    {\n        id: \"2\",\n        name: \"proyekutamainformatika.com\",\n        createdAt: \"1 May 2025\",\n        lastUpdate: \"2 May 2025\",\n        attackCounts: {\n            ddos: 0,\n            sqlInjection: 0,\n            xss: 0\n        },\n        hasActiveAlert: false,\n        securityLogs: [\n            {\n                id: \"1\",\n                timestamp: \"[2025-05-1 10:22:47]\",\n                message: \"No threats detected – normal activity from all sources\",\n                type: \"normal\"\n            },\n            {\n                id: \"2\",\n                timestamp: \"[2025-05-10 10:22:46]\",\n                message: \"No threats detected – normal activity from all sources\",\n                type: \"normal\"\n            },\n            {\n                id: \"3\",\n                timestamp: \"[2025-05-1 10:22:43]\",\n                message: \"Clean traffic observed – no suspicious patterns found\",\n                type: \"normal\"\n            }\n        ],\n        attacks: []\n    },\n    {\n        id: \"3\",\n        name: \"websiteku.org\",\n        createdAt: \"1 May 2025\",\n        lastUpdate: \"2 May 2025\",\n        attackCounts: {\n            ddos: 0,\n            sqlInjection: 0,\n            xss: 0\n        },\n        hasActiveAlert: false,\n        securityLogs: [\n            {\n                id: \"1\",\n                timestamp: \"[2025-05-1 10:22:47]\",\n                message: \"No threats detected – normal activity from all sources\",\n                type: \"normal\"\n            },\n            {\n                id: \"2\",\n                timestamp: \"[2025-05-10 10:22:46]\",\n                message: \"No threats detected – normal activity from all sources\",\n                type: \"normal\"\n            },\n            {\n                id: \"3\",\n                timestamp: \"[2025-05-1 10:22:43]\",\n                message: \"Clean traffic observed – no suspicious patterns found\",\n                type: \"normal\"\n            }\n        ],\n        attacks: []\n    }\n];\nconst getTotalAttacks = ()=>{\n    return mockDomains.reduce((total, domain)=>{\n        return total + domain.attackCounts.ddos + domain.attackCounts.sqlInjection + domain.attackCounts.xss;\n    }, 0);\n};\nconst getTotalAttacksByType = ()=>{\n    return mockDomains.reduce((totals, domain)=>{\n        totals.ddos += domain.attackCounts.ddos;\n        totals.sqlInjection += domain.attackCounts.sqlInjection;\n        totals.xss += domain.attackCounts.xss;\n        return totals;\n    }, {\n        ddos: 0,\n        sqlInjection: 0,\n        xss: 0\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/mockData.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"466fcb249153\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3lyYS1zZWN1cml0eS1kYXNoYm9hcmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzUyYjAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0NjZmY2IyNDkxNTNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\SEMESTER 6\Proyek Utama Informatika\pui\src\app\dashboard\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"SYRA - Secure Your Realm Always\",\n    description: \"Security monitoring dashboard for web applications\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7c0JBQU1IOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3lyYS1zZWN1cml0eS1kYXNoYm9hcmQvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdTWVJBIC0gU2VjdXJlIFlvdXIgUmVhbG0gQWx3YXlzJyxcbiAgZGVzY3JpcHRpb246ICdTZWN1cml0eSBtb25pdG9yaW5nIGRhc2hib2FyZCBmb3Igd2ViIGFwcGxpY2F0aW9ucycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHk+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();