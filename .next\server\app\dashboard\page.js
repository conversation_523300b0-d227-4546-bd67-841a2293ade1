/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\")), \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZkYXNoYm9hcmQlMkZwYWdlJnBhZ2U9JTJGZGFzaGJvYXJkJTJGcGFnZSZhcHBQYXRocz0lMkZkYXNoYm9hcmQlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGZGFzaGJvYXJkJTJGcGFnZS50c3gmYXBwRGlyPUQlM0ElNUNTRU1FU1RFUiUyMDYlNUNQcm95ZWslMjBVdGFtYSUyMEluZm9ybWF0aWthJTVDcHVpJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDU0VNRVNURVIlMjA2JTVDUHJveWVrJTIwVXRhbWElMjBJbmZvcm1hdGlrYSU1Q3B1aSZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGFBQWEsc0JBQXNCO0FBQ2lFO0FBQ3JDO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQyx1QkFBdUIsb0tBQWlIO0FBQ3hJO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EseUJBQXlCLG9KQUF3RztBQUNqSSxvQkFBb0IsME5BQWdGO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUM2RDtBQUNwRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUN1RDtBQUN2RDtBQUNPLHdCQUF3Qiw4R0FBa0I7QUFDakQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3lyYS1zZWN1cml0eS1kYXNoYm9hcmQvP2U0OTkiXSwic291cmNlc0NvbnRlbnQiOlsiXCJUVVJCT1BBQ0sgeyB0cmFuc2l0aW9uOiBuZXh0LXNzciB9XCI7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnZGFzaGJvYXJkJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcU0VNRVNURVIgNlxcXFxQcm95ZWsgVXRhbWEgSW5mb3JtYXRpa2FcXFxccHVpXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpLCBcIkQ6XFxcXFNFTUVTVEVSIDZcXFxcUHJveWVrIFV0YW1hIEluZm9ybWF0aWthXFxcXHB1aVxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcU0VNRVNURVIgNlxcXFxQcm95ZWsgVXRhbWEgSW5mb3JtYXRpa2FcXFxccHVpXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKSwgXCJEOlxcXFxTRU1FU1RFUiA2XFxcXFByb3llayBVdGFtYSBJbmZvcm1hdGlrYVxcXFxwdWlcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIiksIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJEOlxcXFxTRU1FU1RFUiA2XFxcXFByb3llayBVdGFtYSBJbmZvcm1hdGlrYVxcXFxwdWlcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIjtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9kYXNoYm9hcmQvcGFnZVwiO1xuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCI7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9kYXNoYm9hcmQvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvZGFzaGJvYXJkXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCIsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q1NFTUVTVEVSJTIwNiU1Q1Byb3llayUyMFV0YW1hJTIwSW5mb3JtYXRpa2ElNUNwdWklNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNhcHAtcm91dGVyLmpzJm1vZHVsZXM9RCUzQSU1Q1NFTUVTVEVSJTIwNiU1Q1Byb3llayUyMFV0YW1hJTIwSW5mb3JtYXRpa2ElNUNwdWklNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNlcnJvci1ib3VuZGFyeS5qcyZtb2R1bGVzPUQlM0ElNUNTRU1FU1RFUiUyMDYlNUNQcm95ZWslMjBVdGFtYSUyMEluZm9ybWF0aWthJTVDcHVpJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPUQlM0ElNUNTRU1FU1RFUiUyMDYlNUNQcm95ZWslMjBVdGFtYSUyMEluZm9ybWF0aWthJTVDcHVpJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbm90LWZvdW5kLWJvdW5kYXJ5LmpzJm1vZHVsZXM9RCUzQSU1Q1NFTUVTVEVSJTIwNiU1Q1Byb3llayUyMFV0YW1hJTIwSW5mb3JtYXRpa2ElNUNwdWklNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJm1vZHVsZXM9RCUzQSU1Q1NFTUVTVEVSJTIwNiU1Q1Byb3llayUyMFV0YW1hJTIwSW5mb3JtYXRpa2ElNUNwdWklNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQStJO0FBQy9JLDBPQUFtSjtBQUNuSix3T0FBa0o7QUFDbEosa1BBQXVKO0FBQ3ZKLHNRQUFpSztBQUNqSyIsInNvdXJjZXMiOlsid2VicGFjazovL3N5cmEtc2VjdXJpdHktZGFzaGJvYXJkLz80NDgyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcU0VNRVNURVIgNlxcXFxQcm95ZWsgVXRhbWEgSW5mb3JtYXRpa2FcXFxccHVpXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcYXBwLXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcU0VNRVNURVIgNlxcXFxQcm95ZWsgVXRhbWEgSW5mb3JtYXRpa2FcXFxccHVpXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFNFTUVTVEVSIDZcXFxcUHJveWVrIFV0YW1hIEluZm9ybWF0aWthXFxcXHB1aVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFNFTUVTVEVSIDZcXFxcUHJveWVrIFV0YW1hIEluZm9ybWF0aWthXFxcXHB1aVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG5vdC1mb3VuZC1ib3VuZGFyeS5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcU0VNRVNURVIgNlxcXFxQcm95ZWsgVXRhbWEgSW5mb3JtYXRpa2FcXFxccHVpXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcU0VNRVNURVIgNlxcXFxQcm95ZWsgVXRhbWEgSW5mb3JtYXRpa2FcXFxccHVpXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcc3RhdGljLWdlbmVyYXRpb24tc2VhcmNocGFyYW1zLWJhaWxvdXQtcHJvdmlkZXIuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp%5Cdashboard%5Cpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp%5Cdashboard%5Cpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q1NFTUVTVEVSJTIwNiU1Q1Byb3llayUyMFV0YW1hJTIwSW5mb3JtYXRpa2ElNUNwdWklNUNzcmMlNUNhcHAlNUNkYXNoYm9hcmQlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zeXJhLXNlY3VyaXR5LWRhc2hib2FyZC8/ODAyZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFNFTUVTVEVSIDZcXFxcUHJveWVrIFV0YW1hIEluZm9ybWF0aWthXFxcXHB1aVxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp%5Cdashboard%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Navigation */ \"(ssr)/./src/components/Navigation.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Footer */ \"(ssr)/./src/components/Footer.tsx\");\n/* harmony import */ var _components_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Button */ \"(ssr)/./src/components/Button.tsx\");\n/* harmony import */ var _components_AttackStatsCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/AttackStatsCard */ \"(ssr)/./src/components/AttackStatsCard.tsx\");\n/* harmony import */ var _components_DomainCard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/DomainCard */ \"(ssr)/./src/components/DomainCard.tsx\");\n/* harmony import */ var _components_AddDomainModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/AddDomainModal */ \"(ssr)/./src/components/AddDomainModal.tsx\");\n/* harmony import */ var _lib_mockData__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/mockData */ \"(ssr)/./src/lib/mockData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [domains, setDomains] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_mockData__WEBPACK_IMPORTED_MODULE_9__.mockDomains);\n    const attackStats = (0,_lib_mockData__WEBPACK_IMPORTED_MODULE_9__.getTotalAttacksByType)();\n    const handleLogout = ()=>{\n        router.push(\"/\");\n    };\n    const handleAddDomain = (domainName)=>{\n        const newDomain = {\n            id: String(domains.length + 1),\n            name: domainName,\n            createdAt: new Date().toLocaleDateString(\"en-GB\", {\n                day: \"numeric\",\n                month: \"long\",\n                year: \"numeric\"\n            }),\n            lastUpdate: new Date().toLocaleDateString(\"en-GB\", {\n                day: \"numeric\",\n                month: \"long\",\n                year: \"numeric\"\n            }),\n            attackCounts: {\n                ddos: 0,\n                sqlInjection: 0,\n                xss: 0\n            },\n            securityLogs: [\n                {\n                    id: \"1\",\n                    timestamp: \"[\" + new Date().toISOString().slice(0, 16).replace(\"T\", \" \") + \"]\",\n                    message: \"Domain monitoring started - no threats detected\",\n                    type: \"normal\"\n                }\n            ],\n            attacks: [],\n            hasActiveAlert: false\n        };\n        setDomains([\n            ...domains,\n            newDomain\n        ]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onLogout: handleLogout\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 px-6 py-6 max-w-7xl mx-auto w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-6\",\n                                children: \"Total Number of Attacks Detected\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AttackStatsCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        count: attackStats.ddos,\n                                        type: \"DDoS\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AttackStatsCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        count: attackStats.sqlInjection,\n                                        type: \"SQL Injection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AttackStatsCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        count: attackStats.xss,\n                                        type: \"XSS\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"My Domain\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        variant: \"primary\",\n                                        size: \"md\",\n                                        onClick: ()=>setIsModalOpen(true),\n                                        className: \"px-6\",\n                                        children: \"Add New Domain\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                children: domains.map((domain)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DomainCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        domain: domain\n                                    }, domain.id, false, {\n                                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AddDomainModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: isModalOpen,\n                onClose: ()=>setIsModalOpen(false),\n                onSave: handleAddDomain\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Rhc2hib2FyZC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFdUM7QUFDSTtBQUNLO0FBQ1I7QUFDQTtBQUNrQjtBQUNWO0FBQ1E7QUFDVztBQUVwRCxTQUFTVztJQUN0QixNQUFNQyxTQUFTViwwREFBU0E7SUFDeEIsTUFBTSxDQUFDVyxhQUFhQyxlQUFlLEdBQUdiLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ2MsU0FBU0MsV0FBVyxHQUFHZiwrQ0FBUUEsQ0FBQ1Esc0RBQVdBO0lBRWxELE1BQU1RLGNBQWNQLG9FQUFxQkE7SUFFekMsTUFBTVEsZUFBZTtRQUNuQk4sT0FBT08sSUFBSSxDQUFDO0lBQ2Q7SUFFQSxNQUFNQyxrQkFBa0IsQ0FBQ0M7UUFDdkIsTUFBTUMsWUFBWTtZQUNoQkMsSUFBSUMsT0FBT1QsUUFBUVUsTUFBTSxHQUFHO1lBQzVCQyxNQUFNTDtZQUNOTSxXQUFXLElBQUlDLE9BQU9DLGtCQUFrQixDQUFDLFNBQVM7Z0JBQUVDLEtBQUs7Z0JBQVdDLE9BQU87Z0JBQVFDLE1BQU07WUFBVTtZQUNuR0MsWUFBWSxJQUFJTCxPQUFPQyxrQkFBa0IsQ0FBQyxTQUFTO2dCQUFFQyxLQUFLO2dCQUFXQyxPQUFPO2dCQUFRQyxNQUFNO1lBQVU7WUFDcEdFLGNBQWM7Z0JBQUVDLE1BQU07Z0JBQUdDLGNBQWM7Z0JBQUdDLEtBQUs7WUFBRTtZQUNqREMsY0FBYztnQkFDWjtvQkFDRWYsSUFBSTtvQkFDSmdCLFdBQVcsTUFBTSxJQUFJWCxPQUFPWSxXQUFXLEdBQUdDLEtBQUssQ0FBQyxHQUFHLElBQUlDLE9BQU8sQ0FBQyxLQUFLLE9BQU87b0JBQzNFQyxTQUFTO29CQUNUQyxNQUFNO2dCQUNSO2FBQ0Q7WUFDREMsU0FBUyxFQUFFO1lBQ1hDLGdCQUFnQjtRQUNsQjtRQUNBOUIsV0FBVztlQUFJRDtZQUFTTztTQUFVO0lBQ3BDO0lBRUEscUJBQ0UsOERBQUN5QjtRQUFJQyxXQUFVOzswQkFDYiw4REFBQzdDLDhEQUFVQTtnQkFBQzhDLFVBQVUvQjs7Ozs7OzBCQUV0Qiw4REFBQ2dDO2dCQUFLRixXQUFVOztrQ0FFZCw4REFBQ0c7d0JBQVFILFdBQVU7OzBDQUNqQiw4REFBQ0k7Z0NBQUdKLFdBQVU7MENBQXdDOzs7Ozs7MENBR3RELDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUMxQyxtRUFBZUE7d0NBQUMrQyxPQUFPcEMsWUFBWWtCLElBQUk7d0NBQUVTLE1BQUs7Ozs7OztrREFDL0MsOERBQUN0QyxtRUFBZUE7d0NBQUMrQyxPQUFPcEMsWUFBWW1CLFlBQVk7d0NBQUVRLE1BQUs7Ozs7OztrREFDdkQsOERBQUN0QyxtRUFBZUE7d0NBQUMrQyxPQUFPcEMsWUFBWW9CLEdBQUc7d0NBQUVPLE1BQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLbEQsOERBQUNPOzswQ0FDQyw4REFBQ0o7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDSTt3Q0FBR0osV0FBVTtrREFBbUM7Ozs7OztrREFDakQsOERBQUMzQywwREFBTUE7d0NBQ0xpRCxTQUFRO3dDQUNSQyxNQUFLO3dDQUNMQyxTQUFTLElBQU0xQyxlQUFlO3dDQUM5QmtDLFdBQVU7a0RBQ1g7Ozs7Ozs7Ozs7OzswQ0FJSCw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ1pqQyxRQUFRMEMsR0FBRyxDQUFDLENBQUNDLHVCQUNaLDhEQUFDbkQsOERBQVVBO3dDQUFpQm1ELFFBQVFBO3VDQUFuQkEsT0FBT25DLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTWxDLDhEQUFDbkIsMERBQU1BOzs7OzswQkFFUCw4REFBQ0ksa0VBQWNBO2dCQUNibUQsUUFBUTlDO2dCQUNSK0MsU0FBUyxJQUFNOUMsZUFBZTtnQkFDOUIrQyxRQUFRekM7Ozs7Ozs7Ozs7OztBQUloQiIsInNvdXJjZXMiOlsid2VicGFjazovL3N5cmEtc2VjdXJpdHktZGFzaGJvYXJkLy4vc3JjL2FwcC9kYXNoYm9hcmQvcGFnZS50c3g/YzE1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5pbXBvcnQgTmF2aWdhdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvTmF2aWdhdGlvbidcbmltcG9ydCBGb290ZXIgZnJvbSAnQC9jb21wb25lbnRzL0Zvb3RlcidcbmltcG9ydCBCdXR0b24gZnJvbSAnQC9jb21wb25lbnRzL0J1dHRvbidcbmltcG9ydCBBdHRhY2tTdGF0c0NhcmQgZnJvbSAnQC9jb21wb25lbnRzL0F0dGFja1N0YXRzQ2FyZCdcbmltcG9ydCBEb21haW5DYXJkIGZyb20gJ0AvY29tcG9uZW50cy9Eb21haW5DYXJkJ1xuaW1wb3J0IEFkZERvbWFpbk1vZGFsIGZyb20gJ0AvY29tcG9uZW50cy9BZGREb21haW5Nb2RhbCdcbmltcG9ydCB7IG1vY2tEb21haW5zLCBnZXRUb3RhbEF0dGFja3NCeVR5cGUgfSBmcm9tICdAL2xpYi9tb2NrRGF0YSdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGFzaGJvYXJkUGFnZSgpIHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcbiAgY29uc3QgW2lzTW9kYWxPcGVuLCBzZXRJc01vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2RvbWFpbnMsIHNldERvbWFpbnNdID0gdXNlU3RhdGUobW9ja0RvbWFpbnMpXG5cbiAgY29uc3QgYXR0YWNrU3RhdHMgPSBnZXRUb3RhbEF0dGFja3NCeVR5cGUoKVxuXG4gIGNvbnN0IGhhbmRsZUxvZ291dCA9ICgpID0+IHtcbiAgICByb3V0ZXIucHVzaCgnLycpXG4gIH1cblxuICBjb25zdCBoYW5kbGVBZGREb21haW4gPSAoZG9tYWluTmFtZTogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgbmV3RG9tYWluID0ge1xuICAgICAgaWQ6IFN0cmluZyhkb21haW5zLmxlbmd0aCArIDEpLFxuICAgICAgbmFtZTogZG9tYWluTmFtZSxcbiAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0xvY2FsZURhdGVTdHJpbmcoJ2VuLUdCJywgeyBkYXk6ICdudW1lcmljJywgbW9udGg6ICdsb25nJywgeWVhcjogJ251bWVyaWMnIH0pLFxuICAgICAgbGFzdFVwZGF0ZTogbmV3IERhdGUoKS50b0xvY2FsZURhdGVTdHJpbmcoJ2VuLUdCJywgeyBkYXk6ICdudW1lcmljJywgbW9udGg6ICdsb25nJywgeWVhcjogJ251bWVyaWMnIH0pLFxuICAgICAgYXR0YWNrQ291bnRzOiB7IGRkb3M6IDAsIHNxbEluamVjdGlvbjogMCwgeHNzOiAwIH0sXG4gICAgICBzZWN1cml0eUxvZ3M6IFtcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnMScsXG4gICAgICAgICAgdGltZXN0YW1wOiAnWycgKyBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTYpLnJlcGxhY2UoJ1QnLCAnICcpICsgJ10nLFxuICAgICAgICAgIG1lc3NhZ2U6ICdEb21haW4gbW9uaXRvcmluZyBzdGFydGVkIC0gbm8gdGhyZWF0cyBkZXRlY3RlZCcsXG4gICAgICAgICAgdHlwZTogJ25vcm1hbCcgYXMgY29uc3RcbiAgICAgICAgfVxuICAgICAgXSxcbiAgICAgIGF0dGFja3M6IFtdLFxuICAgICAgaGFzQWN0aXZlQWxlcnQ6IGZhbHNlXG4gICAgfVxuICAgIHNldERvbWFpbnMoWy4uLmRvbWFpbnMsIG5ld0RvbWFpbl0pXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgZmxleCBmbGV4LWNvbFwiPlxuICAgICAgPE5hdmlnYXRpb24gb25Mb2dvdXQ9e2hhbmRsZUxvZ291dH0gLz5cbiAgICAgIFxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC0xIHB4LTYgcHktNiBtYXgtdy03eGwgbXgtYXV0byB3LWZ1bGxcIj5cbiAgICAgICAgey8qIEF0dGFjayBTdGF0aXN0aWNzICovfVxuICAgICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTZcIj5cbiAgICAgICAgICAgIFRvdGFsIE51bWJlciBvZiBBdHRhY2tzIERldGVjdGVkXG4gICAgICAgICAgPC9oMj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTYgbWItOFwiPlxuICAgICAgICAgICAgPEF0dGFja1N0YXRzQ2FyZCBjb3VudD17YXR0YWNrU3RhdHMuZGRvc30gdHlwZT1cIkREb1NcIiAvPlxuICAgICAgICAgICAgPEF0dGFja1N0YXRzQ2FyZCBjb3VudD17YXR0YWNrU3RhdHMuc3FsSW5qZWN0aW9ufSB0eXBlPVwiU1FMIEluamVjdGlvblwiIC8+XG4gICAgICAgICAgICA8QXR0YWNrU3RhdHNDYXJkIGNvdW50PXthdHRhY2tTdGF0cy54c3N9IHR5cGU9XCJYU1NcIiAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L3NlY3Rpb24+XG5cbiAgICAgICAgey8qIE15IERvbWFpbnMgU2VjdGlvbiAqL31cbiAgICAgICAgPHNlY3Rpb24+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+TXkgRG9tYWluPC9oMj5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgdmFyaWFudD1cInByaW1hcnlcIlxuICAgICAgICAgICAgICBzaXplPVwibWRcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc01vZGFsT3Blbih0cnVlKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNlwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIEFkZCBOZXcgRG9tYWluXG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTZcIj5cbiAgICAgICAgICAgIHtkb21haW5zLm1hcCgoZG9tYWluKSA9PiAoXG4gICAgICAgICAgICAgIDxEb21haW5DYXJkIGtleT17ZG9tYWluLmlkfSBkb21haW49e2RvbWFpbn0gLz5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L3NlY3Rpb24+XG4gICAgICA8L21haW4+XG5cbiAgICAgIDxGb290ZXIgLz5cblxuICAgICAgPEFkZERvbWFpbk1vZGFsXG4gICAgICAgIGlzT3Blbj17aXNNb2RhbE9wZW59XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldElzTW9kYWxPcGVuKGZhbHNlKX1cbiAgICAgICAgb25TYXZlPXtoYW5kbGVBZGREb21haW59XG4gICAgICAvPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZVJvdXRlciIsIk5hdmlnYXRpb24iLCJGb290ZXIiLCJCdXR0b24iLCJBdHRhY2tTdGF0c0NhcmQiLCJEb21haW5DYXJkIiwiQWRkRG9tYWluTW9kYWwiLCJtb2NrRG9tYWlucyIsImdldFRvdGFsQXR0YWNrc0J5VHlwZSIsIkRhc2hib2FyZFBhZ2UiLCJyb3V0ZXIiLCJpc01vZGFsT3BlbiIsInNldElzTW9kYWxPcGVuIiwiZG9tYWlucyIsInNldERvbWFpbnMiLCJhdHRhY2tTdGF0cyIsImhhbmRsZUxvZ291dCIsInB1c2giLCJoYW5kbGVBZGREb21haW4iLCJkb21haW5OYW1lIiwibmV3RG9tYWluIiwiaWQiLCJTdHJpbmciLCJsZW5ndGgiLCJuYW1lIiwiY3JlYXRlZEF0IiwiRGF0ZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsImRheSIsIm1vbnRoIiwieWVhciIsImxhc3RVcGRhdGUiLCJhdHRhY2tDb3VudHMiLCJkZG9zIiwic3FsSW5qZWN0aW9uIiwieHNzIiwic2VjdXJpdHlMb2dzIiwidGltZXN0YW1wIiwidG9JU09TdHJpbmciLCJzbGljZSIsInJlcGxhY2UiLCJtZXNzYWdlIiwidHlwZSIsImF0dGFja3MiLCJoYXNBY3RpdmVBbGVydCIsImRpdiIsImNsYXNzTmFtZSIsIm9uTG9nb3V0IiwibWFpbiIsInNlY3Rpb24iLCJoMiIsImNvdW50IiwidmFyaWFudCIsInNpemUiLCJvbkNsaWNrIiwibWFwIiwiZG9tYWluIiwiaXNPcGVuIiwib25DbG9zZSIsIm9uU2F2ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AddDomainModal.tsx":
/*!*******************************************!*\
  !*** ./src/components/AddDomainModal.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddDomainModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Button */ \"(ssr)/./src/components/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AddDomainModal({ isOpen, onClose, onSave }) {\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSave = ()=>{\n        if (domain.trim()) {\n            onSave(domain.trim());\n            setDomain(\"\");\n            onClose();\n        }\n    };\n    const handleCancel = ()=>{\n        setDomain(\"\");\n        onClose();\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4 shadow-xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: \"Add New Domain\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600 transition-colors p-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                            children: \"Domain Link\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: domain,\n                            onChange: (e)=>setDomain(e.target.value),\n                            placeholder: \"E.g. trenteknologimobile.com\",\n                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            variant: \"secondary\",\n                            size: \"md\",\n                            onClick: handleCancel,\n                            className: \"flex-1\",\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            variant: \"primary\",\n                            size: \"md\",\n                            onClick: handleSave,\n                            className: \"flex-1\",\n                            children: \"Save\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AddDomainModal.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AddDomainModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AttackStatsCard.tsx":
/*!********************************************!*\
  !*** ./src/components/AttackStatsCard.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AttackStatsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction AttackStatsCard({ count, type }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-8 flex flex-col items-center gap-3 flex-1 min-h-[120px] justify-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-4xl font-bold text-gray-900\",\n                children: count\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackStatsCard.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm text-gray-500 text-center font-medium\",\n                children: type\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackStatsCard.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\AttackStatsCard.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9BdHRhY2tTdGF0c0NhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF5QjtBQU9WLFNBQVNDLGdCQUFnQixFQUFFQyxLQUFLLEVBQUVDLElBQUksRUFBd0I7SUFDM0UscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBS0QsV0FBVTswQkFBb0NIOzs7Ozs7MEJBQ3BELDhEQUFDSTtnQkFBS0QsV0FBVTswQkFBaURGOzs7Ozs7Ozs7Ozs7QUFHdkUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zeXJhLXNlY3VyaXR5LWRhc2hib2FyZC8uL3NyYy9jb21wb25lbnRzL0F0dGFja1N0YXRzQ2FyZC50c3g/OWM5OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBBdHRhY2tTdGF0c0NhcmRQcm9wcyB7XG4gIGNvdW50OiBudW1iZXJcbiAgdHlwZTogc3RyaW5nXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEF0dGFja1N0YXRzQ2FyZCh7IGNvdW50LCB0eXBlIH06IEF0dGFja1N0YXRzQ2FyZFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHJvdW5kZWQtbGcgcC04IGZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGdhcC0zIGZsZXgtMSBtaW4taC1bMTIwcHhdIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPntjb3VudH08L3NwYW4+XG4gICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgdGV4dC1jZW50ZXIgZm9udC1tZWRpdW1cIj57dHlwZX08L3NwYW4+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkF0dGFja1N0YXRzQ2FyZCIsImNvdW50IiwidHlwZSIsImRpdiIsImNsYXNzTmFtZSIsInNwYW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AttackStatsCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Button.tsx":
/*!***********************************!*\
  !*** ./src/components/Button.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n\n\n\nfunction Button({ variant = \"primary\", size = \"md\", children, icon, iconPosition = \"left\", className, ...props }) {\n    const baseClasses = \"inline-flex items-center justify-center font-medium rounded transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2\";\n    const variantClasses = {\n        primary: \"bg-primary text-white hover:bg-primary-600 focus:ring-primary shadow-button\",\n        secondary: \"bg-white text-gray-900 border border-gray-300 hover:bg-gray-50 focus:ring-gray-500\",\n        text: \"text-gray-900 hover:bg-gray-100 focus:ring-gray-500\",\n        danger: \"bg-red-500 text-white hover:bg-red-600 focus:ring-red-500\"\n    };\n    const sizeClasses = {\n        sm: \"px-3 py-1.5 text-sm h-8\",\n        md: \"px-4 py-2 text-base h-10\",\n        lg: \"px-6 py-2.5 text-base h-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(baseClasses, variantClasses[variant], sizeClasses[size], className),\n        ...props,\n        children: [\n            icon && iconPosition === \"left\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this),\n            children,\n            icon && iconPosition === \"right\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Button.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/DomainCard.tsx":
/*!***************************************!*\
  !*** ./src/components/DomainCard.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DomainCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Logo */ \"(ssr)/./src/components/Logo.tsx\");\n/* harmony import */ var _SecurityAlert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SecurityAlert */ \"(ssr)/./src/components/SecurityAlert.tsx\");\n\n\n\n\n\nfunction DomainCard({ domain }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleClick = ()=>{\n        router.push(`/domain/${domain.id}`);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white border border-gray-200 rounded-lg p-6 cursor-pointer hover:shadow-lg transition-shadow\",\n        onClick: handleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        size: \"md\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: domain.name\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-gray-900 mb-3\",\n                        children: \"Security Log:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: domain.securityLogs.slice(0, 3).map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-xs ${log.type === \"error\" ? \"text-red-500\" : \"text-gray-500\"}`,\n                                children: [\n                                    log.timestamp,\n                                    \" \",\n                                    log.message\n                                ]\n                            }, log.id, true, {\n                                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            domain.hasActiveAlert && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SecurityAlert__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                title: \"SQL Injection Detected\",\n                description: \"The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack\"\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\DomainCard.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/DomainCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-white border-t border-gray-200 py-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Copyright 2025 | Nyoman Darmayoga - PUI\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 8,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Footer.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Gb290ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF5QjtBQUdWLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFPQyxXQUFVO2tCQUNoQiw0RUFBQ0M7WUFBSUQsV0FBVTtzQkFDYiw0RUFBQ0M7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNFO29CQUFFRixXQUFVOzhCQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTy9DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3lyYS1zZWN1cml0eS1kYXNoYm9hcmQvLi9zcmMvY29tcG9uZW50cy9Gb290ZXIudHN4PzM1MWYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgQ29weXJpZ2h0IH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBGb290ZXIoKSB7XG4gIHJldHVybiAoXG4gICAgPGZvb3RlciBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXItdCBib3JkZXItZ3JheS0yMDAgcHktNlwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgIENvcHlyaWdodCAyMDI1IHwgTnlvbWFuIERhcm1heW9nYSAtIFBVSVxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Zvb3Rlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiRm9vdGVyIiwiZm9vdGVyIiwiY2xhc3NOYW1lIiwiZGl2IiwicCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Logo.tsx":
/*!*********************************!*\
  !*** ./src/components/Logo.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Logo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction Logo({ size = \"md\", className = \"\" }) {\n    const sizeClasses = {\n        sm: \"w-8 h-8\",\n        md: \"w-10 h-10\",\n        lg: \"w-20 h-20\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${sizeClasses[size]} ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n            src: \"/syra-logo.png\",\n            alt: \"SYRA Logo\",\n            width: size === \"sm\" ? 32 : size === \"md\" ? 40 : 80,\n            height: size === \"sm\" ? 32 : size === \"md\" ? 40 : 80,\n            className: \"w-full h-full object-contain\"\n        }, void 0, false, {\n            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Logo.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Logo.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Mb2dvLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF5QjtBQUVLO0FBT2YsU0FBU0UsS0FBSyxFQUFFQyxPQUFPLElBQUksRUFBRUMsWUFBWSxFQUFFLEVBQWE7SUFDckUsTUFBTUMsY0FBYztRQUNsQkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7SUFDTjtJQUVBLHFCQUNFLDhEQUFDQztRQUFJTCxXQUFXLENBQUMsRUFBRUMsV0FBVyxDQUFDRixLQUFLLENBQUMsQ0FBQyxFQUFFQyxVQUFVLENBQUM7a0JBQ2pELDRFQUFDSCxtREFBS0E7WUFDSlMsS0FBSTtZQUNKQyxLQUFJO1lBQ0pDLE9BQU9ULFNBQVMsT0FBTyxLQUFLQSxTQUFTLE9BQU8sS0FBSztZQUNqRFUsUUFBUVYsU0FBUyxPQUFPLEtBQUtBLFNBQVMsT0FBTyxLQUFLO1lBQ2xEQyxXQUFVOzs7Ozs7Ozs7OztBQUlsQiIsInNvdXJjZXMiOlsid2VicGFjazovL3N5cmEtc2VjdXJpdHktZGFzaGJvYXJkLy4vc3JjL2NvbXBvbmVudHMvTG9nby50c3g/Njc1YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmltcG9ydCBJbWFnZSBmcm9tICduZXh0L2ltYWdlJ1xuXG5pbnRlcmZhY2UgTG9nb1Byb3BzIHtcbiAgc2l6ZT86ICdzbScgfCAnbWQnIHwgJ2xnJ1xuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9nbyh7IHNpemUgPSAnbWQnLCBjbGFzc05hbWUgPSAnJyB9OiBMb2dvUHJvcHMpIHtcbiAgY29uc3Qgc2l6ZUNsYXNzZXMgPSB7XG4gICAgc206ICd3LTggaC04JyxcbiAgICBtZDogJ3ctMTAgaC0xMCcsXG4gICAgbGc6ICd3LTIwIGgtMjAnXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtzaXplQ2xhc3Nlc1tzaXplXX0gJHtjbGFzc05hbWV9YH0+XG4gICAgICA8SW1hZ2VcbiAgICAgICAgc3JjPVwiL3N5cmEtbG9nby5wbmdcIlxuICAgICAgICBhbHQ9XCJTWVJBIExvZ29cIlxuICAgICAgICB3aWR0aD17c2l6ZSA9PT0gJ3NtJyA/IDMyIDogc2l6ZSA9PT0gJ21kJyA/IDQwIDogODB9XG4gICAgICAgIGhlaWdodD17c2l6ZSA9PT0gJ3NtJyA/IDMyIDogc2l6ZSA9PT0gJ21kJyA/IDQwIDogODB9XG4gICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvbnRhaW5cIlxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiSW1hZ2UiLCJMb2dvIiwic2l6ZSIsImNsYXNzTmFtZSIsInNpemVDbGFzc2VzIiwic20iLCJtZCIsImxnIiwiZGl2Iiwic3JjIiwiYWx0Iiwid2lkdGgiLCJoZWlnaHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Logo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Logo */ \"(ssr)/./src/components/Logo.tsx\");\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Button */ \"(ssr)/./src/components/Button.tsx\");\n\n\n\n\nfunction Navigation({ userName = \"I Nyoman Darmayoga\", onLogout }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-sm border-b border-gray-200 px-6 py-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            size: \"lg\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"SYRA\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-semibold text-sm\",\n                                        children: \"I\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-900\",\n                                    children: userName\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            variant: \"primary\",\n                            size: \"sm\",\n                            onClick: onLogout,\n                            children: \"Log Out\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\Navigation.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SecurityAlert.tsx":
/*!******************************************!*\
  !*** ./src/components/SecurityAlert.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SecurityAlert)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction SecurityAlert({ title, description }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-red-50 border border-red-200 rounded-lg p-4 mt-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-4 h-4 bg-red-500 rounded-full flex-shrink-0 mt-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\SecurityAlert.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\SecurityAlert.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                            className: \"text-sm font-semibold text-red-800 mb-2\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\SecurityAlert.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-red-700 leading-relaxed\",\n                            children: description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\SecurityAlert.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\SecurityAlert.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\SecurityAlert.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\components\\\\SecurityAlert.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TZWN1cml0eUFsZXJ0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBeUI7QUFTVixTQUFTQyxjQUFjLEVBQUVDLEtBQUssRUFBRUMsV0FBVyxFQUFzQjtJQUM5RSxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBS0QsV0FBVTtrQ0FBVTs7Ozs7Ozs7Ozs7OEJBRTVCLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNFOzRCQUFHRixXQUFVO3NDQUEyQ0g7Ozs7OztzQ0FDekQsOERBQUNNOzRCQUFFSCxXQUFVO3NDQUF3Q0Y7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSy9EIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3lyYS1zZWN1cml0eS1kYXNoYm9hcmQvLi9zcmMvY29tcG9uZW50cy9TZWN1cml0eUFsZXJ0LnRzeD9iODY4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCB7IEFsZXJ0Q2lyY2xlIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5pbnRlcmZhY2UgU2VjdXJpdHlBbGVydFByb3BzIHtcbiAgdGl0bGU6IHN0cmluZ1xuICBkZXNjcmlwdGlvbjogc3RyaW5nXG4gIHR5cGU/OiAnZXJyb3InIHwgJ3dhcm5pbmcnIHwgJ2luZm8nXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNlY3VyaXR5QWxlcnQoeyB0aXRsZSwgZGVzY3JpcHRpb24gfTogU2VjdXJpdHlBbGVydFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQtbGcgcC00IG10LTRcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBnYXAtM1wiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgYmctcmVkLTUwMCByb3VuZGVkLWZ1bGwgZmxleC1zaHJpbmstMCBtdC0xXCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic3Itb25seVwiPkVycm9yPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtcmVkLTgwMCBtYi0yXCI+e3RpdGxlfTwvaDU+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXJlZC03MDAgbGVhZGluZy1yZWxheGVkXCI+e2Rlc2NyaXB0aW9ufTwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU2VjdXJpdHlBbGVydCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJkaXYiLCJjbGFzc05hbWUiLCJzcGFuIiwiaDUiLCJwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SecurityAlert.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/mockData.ts":
/*!*****************************!*\
  !*** ./src/lib/mockData.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTotalAttacks: () => (/* binding */ getTotalAttacks),\n/* harmony export */   getTotalAttacksByType: () => (/* binding */ getTotalAttacksByType),\n/* harmony export */   mockDomains: () => (/* binding */ mockDomains)\n/* harmony export */ });\nconst mockDomains = [\n    {\n        id: \"1\",\n        name: \"trenteknologimobile.com\",\n        createdAt: \"1 May 2025\",\n        lastUpdate: \"2 May 2025\",\n        attackCounts: {\n            ddos: 0,\n            sqlInjection: 1,\n            xss: 0\n        },\n        hasActiveAlert: true,\n        securityLogs: [\n            {\n                id: \"1\",\n                timestamp: \"[2025-05-1 10:22:45]\",\n                message: \"SQL detected from 192.168.1.77 on /login.php?user=adm4'-- (status: 403)\",\n                type: \"error\"\n            },\n            {\n                id: \"2\",\n                timestamp: \"[2025-05-1 10:22:46]\",\n                message: \"No threats detected – normal activity from all sources\",\n                type: \"normal\"\n            },\n            {\n                id: \"3\",\n                timestamp: \"[2025-05-1 10:22:43]\",\n                message: \"Clean traffic observed – no suspicious patterns found\",\n                type: \"normal\"\n            }\n        ],\n        attacks: [\n            {\n                id: \"1\",\n                type: \"SQL Injection\",\n                status: \"Resolved\",\n                severity: \"Medium\",\n                detectedAt: \"2025-05-1 10:22:45\",\n                resolvedAt: \"2025-05-1 10:23:30\",\n                mitigationDetails: \"The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack\"\n            }\n        ]\n    },\n    {\n        id: \"2\",\n        name: \"proyekutamainformatika.com\",\n        createdAt: \"1 May 2025\",\n        lastUpdate: \"2 May 2025\",\n        attackCounts: {\n            ddos: 0,\n            sqlInjection: 0,\n            xss: 0\n        },\n        hasActiveAlert: false,\n        securityLogs: [\n            {\n                id: \"1\",\n                timestamp: \"[2025-05-1 10:22:47]\",\n                message: \"No threats detected – normal activity from all sources\",\n                type: \"normal\"\n            },\n            {\n                id: \"2\",\n                timestamp: \"[2025-05-10 10:22:46]\",\n                message: \"No threats detected – normal activity from all sources\",\n                type: \"normal\"\n            },\n            {\n                id: \"3\",\n                timestamp: \"[2025-05-1 10:22:43]\",\n                message: \"Clean traffic observed – no suspicious patterns found\",\n                type: \"normal\"\n            }\n        ],\n        attacks: []\n    },\n    {\n        id: \"3\",\n        name: \"websiteku.org\",\n        createdAt: \"1 May 2025\",\n        lastUpdate: \"2 May 2025\",\n        attackCounts: {\n            ddos: 0,\n            sqlInjection: 0,\n            xss: 0\n        },\n        hasActiveAlert: false,\n        securityLogs: [\n            {\n                id: \"1\",\n                timestamp: \"[2025-05-1 10:22:47]\",\n                message: \"No threats detected – normal activity from all sources\",\n                type: \"normal\"\n            },\n            {\n                id: \"2\",\n                timestamp: \"[2025-05-10 10:22:46]\",\n                message: \"No threats detected – normal activity from all sources\",\n                type: \"normal\"\n            },\n            {\n                id: \"3\",\n                timestamp: \"[2025-05-1 10:22:43]\",\n                message: \"Clean traffic observed – no suspicious patterns found\",\n                type: \"normal\"\n            }\n        ],\n        attacks: []\n    }\n];\nconst getTotalAttacks = ()=>{\n    return mockDomains.reduce((total, domain)=>{\n        return total + domain.attackCounts.ddos + domain.attackCounts.sqlInjection + domain.attackCounts.xss;\n    }, 0);\n};\nconst getTotalAttacksByType = ()=>{\n    return mockDomains.reduce((totals, domain)=>{\n        totals.ddos += domain.attackCounts.ddos;\n        totals.sqlInjection += domain.attackCounts.sqlInjection;\n        totals.xss += domain.attackCounts.xss;\n        return totals;\n    }, {\n        ddos: 0,\n        sqlInjection: 0,\n        xss: 0\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL21vY2tEYXRhLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQWdDTyxNQUFNQSxjQUF3QjtJQUNuQztRQUNFQyxJQUFJO1FBQ0pDLE1BQU07UUFDTkMsV0FBVztRQUNYQyxZQUFZO1FBQ1pDLGNBQWM7WUFDWkMsTUFBTTtZQUNOQyxjQUFjO1lBQ2RDLEtBQUs7UUFDUDtRQUNBQyxnQkFBZ0I7UUFDaEJDLGNBQWM7WUFDWjtnQkFDRVQsSUFBSTtnQkFDSlUsV0FBVztnQkFDWEMsU0FBUztnQkFDVEMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VaLElBQUk7Z0JBQ0pVLFdBQVc7Z0JBQ1hDLFNBQVM7Z0JBQ1RDLE1BQU07WUFDUjtZQUNBO2dCQUNFWixJQUFJO2dCQUNKVSxXQUFXO2dCQUNYQyxTQUFTO2dCQUNUQyxNQUFNO1lBQ1I7U0FDRDtRQUNEQyxTQUFTO1lBQ1A7Z0JBQ0ViLElBQUk7Z0JBQ0pZLE1BQU07Z0JBQ05FLFFBQVE7Z0JBQ1JDLFVBQVU7Z0JBQ1ZDLFlBQVk7Z0JBQ1pDLFlBQVk7Z0JBQ1pDLG1CQUFtQjtZQUNyQjtTQUNEO0lBQ0g7SUFDQTtRQUNFbEIsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLFdBQVc7UUFDWEMsWUFBWTtRQUNaQyxjQUFjO1lBQ1pDLE1BQU07WUFDTkMsY0FBYztZQUNkQyxLQUFLO1FBQ1A7UUFDQUMsZ0JBQWdCO1FBQ2hCQyxjQUFjO1lBQ1o7Z0JBQ0VULElBQUk7Z0JBQ0pVLFdBQVc7Z0JBQ1hDLFNBQVM7Z0JBQ1RDLE1BQU07WUFDUjtZQUNBO2dCQUNFWixJQUFJO2dCQUNKVSxXQUFXO2dCQUNYQyxTQUFTO2dCQUNUQyxNQUFNO1lBQ1I7WUFDQTtnQkFDRVosSUFBSTtnQkFDSlUsV0FBVztnQkFDWEMsU0FBUztnQkFDVEMsTUFBTTtZQUNSO1NBQ0Q7UUFDREMsU0FBUyxFQUFFO0lBQ2I7SUFDQTtRQUNFYixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsV0FBVztRQUNYQyxZQUFZO1FBQ1pDLGNBQWM7WUFDWkMsTUFBTTtZQUNOQyxjQUFjO1lBQ2RDLEtBQUs7UUFDUDtRQUNBQyxnQkFBZ0I7UUFDaEJDLGNBQWM7WUFDWjtnQkFDRVQsSUFBSTtnQkFDSlUsV0FBVztnQkFDWEMsU0FBUztnQkFDVEMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VaLElBQUk7Z0JBQ0pVLFdBQVc7Z0JBQ1hDLFNBQVM7Z0JBQ1RDLE1BQU07WUFDUjtZQUNBO2dCQUNFWixJQUFJO2dCQUNKVSxXQUFXO2dCQUNYQyxTQUFTO2dCQUNUQyxNQUFNO1lBQ1I7U0FDRDtRQUNEQyxTQUFTLEVBQUU7SUFDYjtDQUNEO0FBRU0sTUFBTU0sa0JBQWtCO0lBQzdCLE9BQU9wQixZQUFZcUIsTUFBTSxDQUFDLENBQUNDLE9BQU9DO1FBQ2hDLE9BQU9ELFFBQVFDLE9BQU9sQixZQUFZLENBQUNDLElBQUksR0FBR2lCLE9BQU9sQixZQUFZLENBQUNFLFlBQVksR0FBR2dCLE9BQU9sQixZQUFZLENBQUNHLEdBQUc7SUFDdEcsR0FBRztBQUNMLEVBQUM7QUFFTSxNQUFNZ0Isd0JBQXdCO0lBQ25DLE9BQU94QixZQUFZcUIsTUFBTSxDQUFDLENBQUNJLFFBQVFGO1FBQ2pDRSxPQUFPbkIsSUFBSSxJQUFJaUIsT0FBT2xCLFlBQVksQ0FBQ0MsSUFBSTtRQUN2Q21CLE9BQU9sQixZQUFZLElBQUlnQixPQUFPbEIsWUFBWSxDQUFDRSxZQUFZO1FBQ3ZEa0IsT0FBT2pCLEdBQUcsSUFBSWUsT0FBT2xCLFlBQVksQ0FBQ0csR0FBRztRQUNyQyxPQUFPaUI7SUFDVCxHQUFHO1FBQUVuQixNQUFNO1FBQUdDLGNBQWM7UUFBR0MsS0FBSztJQUFFO0FBQ3hDLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zeXJhLXNlY3VyaXR5LWRhc2hib2FyZC8uL3NyYy9saWIvbW9ja0RhdGEudHM/ZmZmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgaW50ZXJmYWNlIFNlY3VyaXR5TG9nIHtcbiAgaWQ6IHN0cmluZ1xuICB0aW1lc3RhbXA6IHN0cmluZ1xuICBtZXNzYWdlOiBzdHJpbmdcbiAgdHlwZTogJ25vcm1hbCcgfCAnd2FybmluZycgfCAnZXJyb3InXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQXR0YWNrRGV0YWlsIHtcbiAgaWQ6IHN0cmluZ1xuICB0eXBlOiAnRERvUycgfCAnU1FMIEluamVjdGlvbicgfCAnWFNTJ1xuICBzdGF0dXM6ICdSZXNvbHZlZCcgfCAnQWN0aXZlJyB8ICdJbnZlc3RpZ2F0aW5nJ1xuICBzZXZlcml0eTogJ0xvdycgfCAnTWVkaXVtJyB8ICdIaWdoJyB8ICdDcml0aWNhbCdcbiAgZGV0ZWN0ZWRBdDogc3RyaW5nXG4gIHJlc29sdmVkQXQ/OiBzdHJpbmdcbiAgbWl0aWdhdGlvbkRldGFpbHM6IHN0cmluZ1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIERvbWFpbiB7XG4gIGlkOiBzdHJpbmdcbiAgbmFtZTogc3RyaW5nXG4gIGNyZWF0ZWRBdDogc3RyaW5nXG4gIGxhc3RVcGRhdGU6IHN0cmluZ1xuICBhdHRhY2tDb3VudHM6IHtcbiAgICBkZG9zOiBudW1iZXJcbiAgICBzcWxJbmplY3Rpb246IG51bWJlclxuICAgIHhzczogbnVtYmVyXG4gIH1cbiAgc2VjdXJpdHlMb2dzOiBTZWN1cml0eUxvZ1tdXG4gIGF0dGFja3M6IEF0dGFja0RldGFpbFtdXG4gIGhhc0FjdGl2ZUFsZXJ0OiBib29sZWFuXG59XG5cbmV4cG9ydCBjb25zdCBtb2NrRG9tYWluczogRG9tYWluW10gPSBbXG4gIHtcbiAgICBpZDogJzEnLFxuICAgIG5hbWU6ICd0cmVudGVrbm9sb2dpbW9iaWxlLmNvbScsXG4gICAgY3JlYXRlZEF0OiAnMSBNYXkgMjAyNScsXG4gICAgbGFzdFVwZGF0ZTogJzIgTWF5IDIwMjUnLFxuICAgIGF0dGFja0NvdW50czoge1xuICAgICAgZGRvczogMCxcbiAgICAgIHNxbEluamVjdGlvbjogMSxcbiAgICAgIHhzczogMFxuICAgIH0sXG4gICAgaGFzQWN0aXZlQWxlcnQ6IHRydWUsXG4gICAgc2VjdXJpdHlMb2dzOiBbXG4gICAgICB7XG4gICAgICAgIGlkOiAnMScsXG4gICAgICAgIHRpbWVzdGFtcDogJ1syMDI1LTA1LTEgMTA6MjI6NDVdJyxcbiAgICAgICAgbWVzc2FnZTogXCJTUUwgZGV0ZWN0ZWQgZnJvbSAxOTIuMTY4LjEuNzcgb24gL2xvZ2luLnBocD91c2VyPWFkbTQnLS0gKHN0YXR1czogNDAzKVwiLFxuICAgICAgICB0eXBlOiAnZXJyb3InXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJzInLFxuICAgICAgICB0aW1lc3RhbXA6ICdbMjAyNS0wNS0xIDEwOjIyOjQ2XScsXG4gICAgICAgIG1lc3NhZ2U6ICdObyB0aHJlYXRzIGRldGVjdGVkIOKAkyBub3JtYWwgYWN0aXZpdHkgZnJvbSBhbGwgc291cmNlcycsXG4gICAgICAgIHR5cGU6ICdub3JtYWwnXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJzMnLFxuICAgICAgICB0aW1lc3RhbXA6ICdbMjAyNS0wNS0xIDEwOjIyOjQzXScsXG4gICAgICAgIG1lc3NhZ2U6ICdDbGVhbiB0cmFmZmljIG9ic2VydmVkIOKAkyBubyBzdXNwaWNpb3VzIHBhdHRlcm5zIGZvdW5kJyxcbiAgICAgICAgdHlwZTogJ25vcm1hbCdcbiAgICAgIH1cbiAgICBdLFxuICAgIGF0dGFja3M6IFtcbiAgICAgIHtcbiAgICAgICAgaWQ6ICcxJyxcbiAgICAgICAgdHlwZTogJ1NRTCBJbmplY3Rpb24nLFxuICAgICAgICBzdGF0dXM6ICdSZXNvbHZlZCcsXG4gICAgICAgIHNldmVyaXR5OiAnTWVkaXVtJyxcbiAgICAgICAgZGV0ZWN0ZWRBdDogJzIwMjUtMDUtMSAxMDoyMjo0NScsXG4gICAgICAgIHJlc29sdmVkQXQ6ICcyMDI1LTA1LTEgMTA6MjM6MzAnLFxuICAgICAgICBtaXRpZ2F0aW9uRGV0YWlsczogJ1RoZSBzeXN0ZW0gaGFzIGF1dG9tYXRpY2FsbHkgYmxvY2tlZCBhIG1hbGljaW91cyBJUCBhbmQgaXNvbGF0ZWQgYWZmZWN0ZWQgY29tcG9uZW50cyB0byBwcmV2ZW50IHRoZSBzcHJlYWQgb2YgdGhlIGF0dGFjaydcbiAgICAgIH1cbiAgICBdXG4gIH0sXG4gIHtcbiAgICBpZDogJzInLFxuICAgIG5hbWU6ICdwcm95ZWt1dGFtYWluZm9ybWF0aWthLmNvbScsXG4gICAgY3JlYXRlZEF0OiAnMSBNYXkgMjAyNScsXG4gICAgbGFzdFVwZGF0ZTogJzIgTWF5IDIwMjUnLFxuICAgIGF0dGFja0NvdW50czoge1xuICAgICAgZGRvczogMCxcbiAgICAgIHNxbEluamVjdGlvbjogMCxcbiAgICAgIHhzczogMFxuICAgIH0sXG4gICAgaGFzQWN0aXZlQWxlcnQ6IGZhbHNlLFxuICAgIHNlY3VyaXR5TG9nczogW1xuICAgICAge1xuICAgICAgICBpZDogJzEnLFxuICAgICAgICB0aW1lc3RhbXA6ICdbMjAyNS0wNS0xIDEwOjIyOjQ3XScsXG4gICAgICAgIG1lc3NhZ2U6ICdObyB0aHJlYXRzIGRldGVjdGVkIOKAkyBub3JtYWwgYWN0aXZpdHkgZnJvbSBhbGwgc291cmNlcycsXG4gICAgICAgIHR5cGU6ICdub3JtYWwnXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJzInLFxuICAgICAgICB0aW1lc3RhbXA6ICdbMjAyNS0wNS0xMCAxMDoyMjo0Nl0nLFxuICAgICAgICBtZXNzYWdlOiAnTm8gdGhyZWF0cyBkZXRlY3RlZCDigJMgbm9ybWFsIGFjdGl2aXR5IGZyb20gYWxsIHNvdXJjZXMnLFxuICAgICAgICB0eXBlOiAnbm9ybWFsJ1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6ICczJyxcbiAgICAgICAgdGltZXN0YW1wOiAnWzIwMjUtMDUtMSAxMDoyMjo0M10nLFxuICAgICAgICBtZXNzYWdlOiAnQ2xlYW4gdHJhZmZpYyBvYnNlcnZlZCDigJMgbm8gc3VzcGljaW91cyBwYXR0ZXJucyBmb3VuZCcsXG4gICAgICAgIHR5cGU6ICdub3JtYWwnXG4gICAgICB9XG4gICAgXSxcbiAgICBhdHRhY2tzOiBbXVxuICB9LFxuICB7XG4gICAgaWQ6ICczJyxcbiAgICBuYW1lOiAnd2Vic2l0ZWt1Lm9yZycsXG4gICAgY3JlYXRlZEF0OiAnMSBNYXkgMjAyNScsXG4gICAgbGFzdFVwZGF0ZTogJzIgTWF5IDIwMjUnLFxuICAgIGF0dGFja0NvdW50czoge1xuICAgICAgZGRvczogMCxcbiAgICAgIHNxbEluamVjdGlvbjogMCxcbiAgICAgIHhzczogMFxuICAgIH0sXG4gICAgaGFzQWN0aXZlQWxlcnQ6IGZhbHNlLFxuICAgIHNlY3VyaXR5TG9nczogW1xuICAgICAge1xuICAgICAgICBpZDogJzEnLFxuICAgICAgICB0aW1lc3RhbXA6ICdbMjAyNS0wNS0xIDEwOjIyOjQ3XScsXG4gICAgICAgIG1lc3NhZ2U6ICdObyB0aHJlYXRzIGRldGVjdGVkIOKAkyBub3JtYWwgYWN0aXZpdHkgZnJvbSBhbGwgc291cmNlcycsXG4gICAgICAgIHR5cGU6ICdub3JtYWwnXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJzInLFxuICAgICAgICB0aW1lc3RhbXA6ICdbMjAyNS0wNS0xMCAxMDoyMjo0Nl0nLFxuICAgICAgICBtZXNzYWdlOiAnTm8gdGhyZWF0cyBkZXRlY3RlZCDigJMgbm9ybWFsIGFjdGl2aXR5IGZyb20gYWxsIHNvdXJjZXMnLFxuICAgICAgICB0eXBlOiAnbm9ybWFsJ1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6ICczJyxcbiAgICAgICAgdGltZXN0YW1wOiAnWzIwMjUtMDUtMSAxMDoyMjo0M10nLFxuICAgICAgICBtZXNzYWdlOiAnQ2xlYW4gdHJhZmZpYyBvYnNlcnZlZCDigJMgbm8gc3VzcGljaW91cyBwYXR0ZXJucyBmb3VuZCcsXG4gICAgICAgIHR5cGU6ICdub3JtYWwnXG4gICAgICB9XG4gICAgXSxcbiAgICBhdHRhY2tzOiBbXVxuICB9XG5dXG5cbmV4cG9ydCBjb25zdCBnZXRUb3RhbEF0dGFja3MgPSAoKSA9PiB7XG4gIHJldHVybiBtb2NrRG9tYWlucy5yZWR1Y2UoKHRvdGFsLCBkb21haW4pID0+IHtcbiAgICByZXR1cm4gdG90YWwgKyBkb21haW4uYXR0YWNrQ291bnRzLmRkb3MgKyBkb21haW4uYXR0YWNrQ291bnRzLnNxbEluamVjdGlvbiArIGRvbWFpbi5hdHRhY2tDb3VudHMueHNzXG4gIH0sIDApXG59XG5cbmV4cG9ydCBjb25zdCBnZXRUb3RhbEF0dGFja3NCeVR5cGUgPSAoKSA9PiB7XG4gIHJldHVybiBtb2NrRG9tYWlucy5yZWR1Y2UoKHRvdGFscywgZG9tYWluKSA9PiB7XG4gICAgdG90YWxzLmRkb3MgKz0gZG9tYWluLmF0dGFja0NvdW50cy5kZG9zXG4gICAgdG90YWxzLnNxbEluamVjdGlvbiArPSBkb21haW4uYXR0YWNrQ291bnRzLnNxbEluamVjdGlvblxuICAgIHRvdGFscy54c3MgKz0gZG9tYWluLmF0dGFja0NvdW50cy54c3NcbiAgICByZXR1cm4gdG90YWxzXG4gIH0sIHsgZGRvczogMCwgc3FsSW5qZWN0aW9uOiAwLCB4c3M6IDAgfSlcbn1cbiJdLCJuYW1lcyI6WyJtb2NrRG9tYWlucyIsImlkIiwibmFtZSIsImNyZWF0ZWRBdCIsImxhc3RVcGRhdGUiLCJhdHRhY2tDb3VudHMiLCJkZG9zIiwic3FsSW5qZWN0aW9uIiwieHNzIiwiaGFzQWN0aXZlQWxlcnQiLCJzZWN1cml0eUxvZ3MiLCJ0aW1lc3RhbXAiLCJtZXNzYWdlIiwidHlwZSIsImF0dGFja3MiLCJzdGF0dXMiLCJzZXZlcml0eSIsImRldGVjdGVkQXQiLCJyZXNvbHZlZEF0IiwibWl0aWdhdGlvbkRldGFpbHMiLCJnZXRUb3RhbEF0dGFja3MiLCJyZWR1Y2UiLCJ0b3RhbCIsImRvbWFpbiIsImdldFRvdGFsQXR0YWNrc0J5VHlwZSIsInRvdGFscyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/mockData.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"466fcb249153\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3lyYS1zZWN1cml0eS1kYXNoYm9hcmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzUyYjAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0NjZmY2IyNDkxNTNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\SEMESTER 6\Proyek Utama Informatika\pui\src\app\dashboard\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"SYRA - Secure Your Realm Always\",\n    description: \"Security monitoring dashboard for web applications\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\SEMESTER 6\\\\Proyek Utama Informatika\\\\pui\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7c0JBQU1IOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3lyYS1zZWN1cml0eS1kYXNoYm9hcmQvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdTWVJBIC0gU2VjdXJlIFlvdXIgUmVhbG0gQWx3YXlzJyxcbiAgZGVzY3JpcHRpb246ICdTZWN1cml0eSBtb25pdG9yaW5nIGRhc2hib2FyZCBmb3Igd2ViIGFwcGxpY2F0aW9ucycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHk+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSEMESTER%206%5CProyek%20Utama%20Informatika%5Cpui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();