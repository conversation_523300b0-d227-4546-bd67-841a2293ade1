{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/internal/ReactDevOverlay.tsx"], "names": ["ReactDevOverlay", "React", "PureComponent", "getDerivedStateFromError", "error", "e", "event", "type", "ACTION_UNHANDLED_ERROR", "reason", "frames", "parseStack", "stack", "errorEvent", "id", "reactError", "componentDidCatch", "componentErr", "props", "onReactError", "render", "state", "children", "hasBuildError", "buildError", "hasRuntimeErrors", "Boolean", "errors", "length", "rootLayoutMissingTagsError", "isMounted", "html", "head", "body", "ShadowPort<PERSON>", "CssReset", "Base", "ComponentStyles", "RootLayoutError", "missingTags", "BuildError", "message", "versionInfo", "Errors", "initialDisplayState", "undefined"], "mappings": ";;;;+BA0GA;;;eAAA;;;;iEA1GuB;qCACgB;8BAMV;4BACF;wBACJ;iCAES;4BACL;sBACN;iCACW;0BACP;AAKzB,MAAMA,wBAAwBC,OAAMC,aAAa;IAU/C,OAAOC,yBAAyBC,KAAY,EAAwB;QAClE,MAAMC,IAAID;QACV,MAAME,QAA8B;YAClCC,MAAMC,2CAAsB;YAC5BC,QAAQL;YACRM,QAAQC,IAAAA,sBAAU,EAACN,EAAEO,KAAK;QAC5B;QACA,MAAMC,aAAkC;YACtCC,IAAI;YACJR;QACF;QACA,OAAO;YAAES,YAAYF;QAAW;IAClC;IAEAG,kBAAkBC,YAAmB,EAAE;QACrC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACF;IAC1B;IAEAG,SAAS;QACP,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAE,GAAG,IAAI,CAACJ,KAAK;QACtC,MAAM,EAAEH,UAAU,EAAE,GAAG,IAAI,CAACM,KAAK;QAEjC,MAAME,gBAAgBF,MAAMG,UAAU,IAAI;QAC1C,MAAMC,mBAAmBC,QAAQL,MAAMM,MAAM,CAACC,MAAM;QACpD,MAAMC,6BAA6BR,MAAMQ,0BAA0B;QACnE,MAAMC,YACJP,iBACAE,oBACAV,cACAc;QAEF,qBACE,4CACGd,2BACC,qBAACgB,4BACC,qBAACC,6BACD,qBAACC,iBAGHX,UAEDQ,0BACC,qBAACI,0BAAY,sBACX,qBAACC,kBAAQ,uBACT,qBAACC,UAAI,uBACL,qBAACC,gCAAe,SAEfR,2CACC,qBAACS,gCAAe;YACdC,aAAaV,2BAA2BU,WAAW;aAEnDhB,8BACF,qBAACiB,sBAAU;YACTC,SAASpB,MAAMG,UAAU;YACzBkB,aAAarB,MAAMqB,WAAW;aAE9B3B,2BACF,qBAAC4B,cAAM;YACLD,aAAarB,MAAMqB,WAAW;YAC9BE,qBAAoB;YACpBjB,QAAQ;gBAACZ;aAAW;aAEpBU,iCACF,qBAACkB,cAAM;YACLC,qBAAoB;YACpBjB,QAAQN,MAAMM,MAAM;YACpBe,aAAarB,MAAMqB,WAAW;aAE9BG,aAEJA;IAGV;;;aA3EAxB,QAAQ;YAAEN,YAAY;QAAK;;AA4E7B;MAEA,WAAef"}