"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const buffer_1 = require("buffer");
const crypto_1 = require("crypto");
const base64url_js_1 = require("./base64url.js");
const errors_js_1 = require("../util/errors.js");
const get_named_curve_js_1 = require("./get_named_curve.js");
const check_modulus_length_js_1 = require("./check_modulus_length.js");
const asn1_sequence_encoder_js_1 = require("./asn1_sequence_encoder.js");
const flags_js_1 = require("./flags.js");
const parse = (jwk) => {
    if (flags_js_1.jwkImport && jwk.kty !== 'oct') {
        return jwk.d
            ? (0, crypto_1.createPrivateKey)({ format: 'jwk', key: jwk })
            : (0, crypto_1.createPublicKey)({ format: 'jwk', key: jwk });
    }
    switch (jwk.kty) {
        case 'oct': {
            return (0, crypto_1.createSecretKey)((0, base64url_js_1.decode)(jwk.k));
        }
        case 'RSA': {
            const enc = new asn1_sequence_encoder_js_1.default();
            const isPrivate = jwk.d !== undefined;
            const modulus = buffer_1.Buffer.from(jwk.n, 'base64');
            const exponent = buffer_1.Buffer.from(jwk.e, 'base64');
            if (isPrivate) {
                enc.zero();
                enc.unsignedInteger(modulus);
                enc.unsignedInteger(exponent);
                enc.unsignedInteger(buffer_1.Buffer.from(jwk.d, 'base64'));
                enc.unsignedInteger(buffer_1.Buffer.from(jwk.p, 'base64'));
                enc.unsignedInteger(buffer_1.Buffer.from(jwk.q, 'base64'));
                enc.unsignedInteger(buffer_1.Buffer.from(jwk.dp, 'base64'));
                enc.unsignedInteger(buffer_1.Buffer.from(jwk.dq, 'base64'));
                enc.unsignedInteger(buffer_1.Buffer.from(jwk.qi, 'base64'));
            }
            else {
                enc.unsignedInteger(modulus);
                enc.unsignedInteger(exponent);
            }
            const der = enc.end();
            const createInput = {
                key: der,
                format: 'der',
                type: 'pkcs1',
            };
            const keyObject = isPrivate ? (0, crypto_1.createPrivateKey)(createInput) : (0, crypto_1.createPublicKey)(createInput);
            (0, check_modulus_length_js_1.setModulusLength)(keyObject, modulus.length << 3);
            return keyObject;
        }
        case 'EC': {
            const enc = new asn1_sequence_encoder_js_1.default();
            const isPrivate = jwk.d !== undefined;
            const pub = buffer_1.Buffer.concat([
                buffer_1.Buffer.alloc(1, 4),
                buffer_1.Buffer.from(jwk.x, 'base64'),
                buffer_1.Buffer.from(jwk.y, 'base64'),
            ]);
            if (isPrivate) {
                enc.zero();
                const enc$1 = new asn1_sequence_encoder_js_1.default();
                enc$1.oidFor('ecPublicKey');
                enc$1.oidFor(jwk.crv);
                enc.add(enc$1.end());
                const enc$2 = new asn1_sequence_encoder_js_1.default();
                enc$2.one();
                enc$2.octStr(buffer_1.Buffer.from(jwk.d, 'base64'));
                const enc$3 = new asn1_sequence_encoder_js_1.default();
                enc$3.bitStr(pub);
                const f2 = enc$3.end(buffer_1.Buffer.from([0xa1]));
                enc$2.add(f2);
                const f = enc$2.end();
                const enc$4 = new asn1_sequence_encoder_js_1.default();
                enc$4.add(f);
                const f3 = enc$4.end(buffer_1.Buffer.from([0x04]));
                enc.add(f3);
                const der = enc.end();
                const keyObject = (0, crypto_1.createPrivateKey)({ key: der, format: 'der', type: 'pkcs8' });
                (0, get_named_curve_js_1.setCurve)(keyObject, jwk.crv);
                return keyObject;
            }
            const enc$1 = new asn1_sequence_encoder_js_1.default();
            enc$1.oidFor('ecPublicKey');
            enc$1.oidFor(jwk.crv);
            enc.add(enc$1.end());
            enc.bitStr(pub);
            const der = enc.end();
            const keyObject = (0, crypto_1.createPublicKey)({ key: der, format: 'der', type: 'spki' });
            (0, get_named_curve_js_1.setCurve)(keyObject, jwk.crv);
            return keyObject;
        }
        case 'OKP': {
            const enc = new asn1_sequence_encoder_js_1.default();
            const isPrivate = jwk.d !== undefined;
            if (isPrivate) {
                enc.zero();
                const enc$1 = new asn1_sequence_encoder_js_1.default();
                enc$1.oidFor(jwk.crv);
                enc.add(enc$1.end());
                const enc$2 = new asn1_sequence_encoder_js_1.default();
                enc$2.octStr(buffer_1.Buffer.from(jwk.d, 'base64'));
                const f = enc$2.end(buffer_1.Buffer.from([0x04]));
                enc.add(f);
                const der = enc.end();
                return (0, crypto_1.createPrivateKey)({ key: der, format: 'der', type: 'pkcs8' });
            }
            const enc$1 = new asn1_sequence_encoder_js_1.default();
            enc$1.oidFor(jwk.crv);
            enc.add(enc$1.end());
            enc.bitStr(buffer_1.Buffer.from(jwk.x, 'base64'));
            const der = enc.end();
            return (0, crypto_1.createPublicKey)({ key: der, format: 'der', type: 'spki' });
        }
        default:
            throw new errors_js_1.JOSENotSupported('Invalid or unsupported JWK "kty" (Key Type) Parameter value');
    }
};
exports.default = parse;
