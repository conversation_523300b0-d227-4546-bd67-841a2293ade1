import React from 'react'

interface AttackStatsCardProps {
  count: number
  type: string
}

export default function AttackStatsCard({ count, type }: AttackStatsCardProps) {
  return (
    <div className="bg-gray-50 rounded-lg p-8 flex flex-col items-center gap-3 flex-1 min-h-[120px] justify-center">
      <span className="text-4xl font-bold text-gray-900">{count}</span>
      <span className="text-sm text-gray-500 text-center font-medium">{type}</span>
    </div>
  )
}
