# 🚀 SYRA Setup Guide

Panduan lengkap untuk mengatur dan menja<PERSON>an aplikasi SYRA (Secure Your Realm Always).

## 📋 Prerequisites

1. **Node.js** (v18 atau lebih baru)
2. **PostgreSQL** (v12 atau lebih baru)
3. **Google Cloud Console Account** (untuk OAuth)
4. **npm** atau **yarn**

## 🗄️ Setup Database PostgreSQL

### 1. Install PostgreSQL
- Download dan install PostgreSQL dari [postgresql.org](https://www.postgresql.org/download/)
- Atau gunakan Docker: `docker run --name postgres -e POSTGRES_PASSWORD=password -p 5432:5432 -d postgres`

### 2. Buat Database
```sql
-- Login ke PostgreSQL
psql -U postgres

-- Buat database
CREATE DATABASE syra_security;

-- Buat user (opsional)
CREATE USER syra_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE syra_security TO syra_user;
```

## 🔐 Setup Google OAuth

### 1. Google Cloud Console
1. Buka [Google Cloud Console](https://console.cloud.google.com/)
2. Buat project baru atau pilih project yang ada
3. Enable Google+ API dan Google OAuth2 API

### 2. Buat OAuth 2.0 Credentials
1. Pergi ke **APIs & Services > Credentials**
2. Klik **Create Credentials > OAuth 2.0 Client IDs**
3. Pilih **Web application**
4. Tambahkan **Authorized redirect URIs**:
   - `http://localhost:3000/api/auth/callback/google`
5. Simpan **Client ID** dan **Client Secret**

## ⚙️ Konfigurasi Environment

### Backend (.env)
```bash
cd backend
cp .env.example .env
```

Edit file `backend/.env`:
```env
PORT=5000
NODE_ENV=development
JWT_SECRET=your_super_secret_jwt_key_here

# PostgreSQL
DATABASE_URL="postgresql://username:password@localhost:5432/syra_security?schema=public"
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=syra_security
POSTGRES_USER=your_username
POSTGRES_PASSWORD=your_password

# CORS
CORS_ORIGIN=http://localhost:3000
```

### Frontend (.env.local)
```bash
cd frontend
```

Edit file `frontend/.env.local`:
```env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key-here

# Google OAuth (dari Google Cloud Console)
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Backend API
NEXT_PUBLIC_API_URL=http://localhost:5000/api
```

## 📦 Instalasi Dependencies

### Backend
```bash
cd backend
npm install
```

### Frontend
```bash
cd frontend
npm install
```

## 🗃️ Setup Database Schema

```bash
cd backend

# Generate Prisma client
npm run db:generate

# Push schema ke database
npm run db:push

# (Opsional) Buka Prisma Studio untuk melihat data
npm run db:studio
```

## 🚀 Menjalankan Aplikasi

### 1. Jalankan Backend
```bash
cd backend
npm run dev
```
Backend akan berjalan di: http://localhost:5000

### 2. Jalankan Frontend
```bash
cd frontend
npm run dev
```
Frontend akan berjalan di: http://localhost:3000

## 🧪 Testing Setup

### 1. Test Backend API
```bash
curl http://localhost:5000/api/health
```

Response yang diharapkan:
```json
{
  "status": "OK",
  "message": "SYRA Backend API is running",
  "timestamp": "2025-01-XX...",
  "version": "1.0.0"
}
```

### 2. Test Frontend
1. Buka browser ke http://localhost:3000
2. Klik "Sign in with Google"
3. Login dengan akun Google
4. Redirect ke dashboard

## 🔧 Troubleshooting

### Database Connection Error
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Restart PostgreSQL
sudo systemctl restart postgresql

# Test connection
psql -U username -d syra_security -h localhost -p 5432
```

### Google OAuth Error
1. Pastikan **Authorized redirect URIs** sudah benar
2. Check **Client ID** dan **Client Secret** di `.env.local`
3. Pastikan Google+ API sudah enabled

### Port Already in Use
```bash
# Kill process on port 3000
npx kill-port 3000

# Kill process on port 5000
npx kill-port 5000
```

## 📁 Struktur File Penting

```
pui/
├── frontend/
│   ├── .env.local              # Environment variables
│   ├── public/syra-logo.png    # Logo aplikasi
│   └── src/
│       ├── app/api/auth/       # NextAuth API routes
│       ├── components/         # React components
│       └── lib/auth.ts         # Auth configuration
├── backend/
│   ├── .env                    # Environment variables
│   ├── prisma/schema.prisma    # Database schema
│   └── src/
│       ├── lib/prisma.js       # Database connection
│       └── routes/             # API routes
└── assets/
    └── syra-logo.png           # Original logo
```

## 🎯 Fitur yang Sudah Diimplementasi

✅ **Logo dari Assets** - Menggunakan gambar logo yang konsisten  
✅ **Google OAuth Login** - Autentikasi dengan akun Google  
✅ **PostgreSQL Database** - Penyimpanan data dengan Prisma ORM  
✅ **User Management** - Registrasi dan login otomatis  
✅ **Domain Management** - CRUD operations untuk domain  
✅ **Security Monitoring** - Log keamanan dan deteksi serangan  
✅ **Responsive Design** - UI yang mobile-friendly  

## 📞 Support

Jika mengalami masalah, silakan:
1. Check log di terminal backend dan frontend
2. Pastikan semua environment variables sudah benar
3. Verify database connection
4. Test Google OAuth credentials

**Developer**: I Nyoman Darmayoga  
**Project**: Proyek Utama Informatika (PUI)  
**Year**: 2025
