{"version": 3, "sources": ["../../../src/server/lib/mock-request.ts"], "names": ["Stream", "fromNodeOutgoingHttpHeaders", "toNodeOutgoingHttpHeaders", "MockedRequest", "Readable", "constructor", "url", "headers", "method", "socket", "readable", "httpVersion", "httpVersionMajor", "httpVersionMinor", "Proxy", "get", "_target", "prop", "Error", "bodyReadable", "on", "emit", "headersDistinct", "key", "value", "Object", "entries", "Array", "isArray", "_read", "size", "connection", "aborted", "complete", "trailers", "trailersDistinct", "rawTrailers", "rawHeaders", "setTimeout", "MockedResponse", "Writable", "res", "statusMessage", "finished", "headersSent", "buffers", "statusCode", "Headers", "head<PERSON><PERSON><PERSON>", "Promise", "resolve", "headPromiseResolve", "hasStreamed", "reject", "err", "then", "val", "resWriter", "append<PERSON><PERSON>er", "name", "values", "v", "append", "isSent", "write", "chunk", "push", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "from", "end", "arguments", "_implicitHeader", "_write", "_encoding", "callback", "writeHead", "length", "i", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "has", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "getHeaders", "getHeaderNames", "keys", "delete", "set", "toString", "removeHeader", "flushHeaders", "strictContent<PERSON>ength", "writeEarlyHints", "req", "assignSocket", "detachSocket", "writeContinue", "writeProcessing", "upgrading", "chunkedEncoding", "shouldKeepAlive", "useChunkedEncodingByDefault", "sendDate", "addTrailers", "createRequestResponseMocks"], "mappings": "AAUA,OAAOA,YAAY,SAAQ;AAC3B,SACEC,2BAA2B,EAC3BC,yBAAyB,QACpB,eAAc;AAUrB,OAAO,MAAMC,sBAAsBH,OAAOI,QAAQ;IA4BhDC,YAAY,EACVC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,SAAS,IAAI,EACbC,QAAQ,EACa,CAAE;QACvB,KAAK;QA5BP,8EAA8E;aAC9DC,cAAc;aACdC,mBAAmB;aACnBC,mBAAmB;QAInC,qEAAqE;QACrE,qDAAqD;aAC9CJ,SAAiB,IAAIK,MAAiB,CAAC,GAAgB;YAC5DC,KAAK,CAACC,SAASC;gBACb,IAAIA,SAAS,aAAa;oBACxB,MAAM,IAAIC,MAAM;gBAClB;gBAEA,0EAA0E;gBAC1E,kDAAkD;gBAClD,OAAO;YACT;QACF;QAWE,IAAI,CAACZ,GAAG,GAAGA;QACX,IAAI,CAACC,OAAO,GAAGA;QACf,IAAI,CAACC,MAAM,GAAGA;QAEd,IAAIE,UAAU;YACZ,IAAI,CAACS,YAAY,GAAGT;YACpB,IAAI,CAACS,YAAY,CAACC,EAAE,CAAC,OAAO,IAAM,IAAI,CAACC,IAAI,CAAC;YAC5C,IAAI,CAACF,YAAY,CAACC,EAAE,CAAC,SAAS,IAAM,IAAI,CAACC,IAAI,CAAC;QAChD;QAEA,IAAIZ,QAAQ;YACV,IAAI,CAACA,MAAM,GAAGA;QAChB;IACF;IAEA,IAAWa,kBAAyC;QAClD,MAAMf,UAAiC,CAAC;QACxC,KAAK,MAAM,CAACgB,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAAC,IAAI,CAACnB,OAAO,EAAG;YACvD,IAAI,CAACiB,OAAO;YAEZjB,OAAO,CAACgB,IAAI,GAAGI,MAAMC,OAAO,CAACJ,SAASA,QAAQ;gBAACA;aAAM;QACvD;QAEA,OAAOjB;IACT;IAEOsB,MAAMC,IAAY,EAAQ;QAC/B,IAAI,IAAI,CAACX,YAAY,EAAE;YACrB,OAAO,IAAI,CAACA,YAAY,CAACU,KAAK,CAACC;QACjC,OAAO;YACL,IAAI,CAACT,IAAI,CAAC;YACV,IAAI,CAACA,IAAI,CAAC;QACZ;IACF;IAEA;;;;GAIC,GACD,IAAWU,aAAqB;QAC9B,OAAO,IAAI,CAACtB,MAAM;IACpB;IAEA,wEAAwE;IACxE,oBAAoB;IAEpB,IAAWuB,UAAmB;QAC5B,MAAM,IAAId,MAAM;IAClB;IAEA,IAAWe,WAAoB;QAC7B,MAAM,IAAIf,MAAM;IAClB;IAEA,IAAWgB,WAAgC;QACzC,MAAM,IAAIhB,MAAM;IAClB;IAEA,IAAWiB,mBAA0C;QACnD,MAAM,IAAIjB,MAAM;IAClB;IAEA,IAAWkB,cAAwB;QACjC,MAAM,IAAIlB,MAAM;IAClB;IAEA,IAAWmB,aAAuB;QAChC,MAAM,IAAInB,MAAM;IAClB;IAEOoB,aAAmB;QACxB,MAAM,IAAIpB,MAAM;IAClB;AACF;AASA,OAAO,MAAMqB,uBAAuBvC,OAAOwC,QAAQ;IAkCjDnC,YAAYoC,MAA6B,CAAC,CAAC,CAAE;QAC3C,KAAK;aAjCAC,gBAAwB;aACxBC,WAAW;aACXC,cAAc;QAUrB;;;;GAIC,QACeC,UAAoB,EAAE;QAkBpC,IAAI,CAACC,UAAU,GAAGL,IAAIK,UAAU,IAAI;QACpC,IAAI,CAACrC,MAAM,GAAGgC,IAAIhC,MAAM,IAAI;QAC5B,IAAI,CAACF,OAAO,GAAGkC,IAAIlC,OAAO,GACtBN,4BAA4BwC,IAAIlC,OAAO,IACvC,IAAIwC;QAER,IAAI,CAACC,WAAW,GAAG,IAAIC,QAAc,CAACC;YACpC,IAAI,CAACC,kBAAkB,GAAGD;QAC5B;QAEA,sEAAsE;QACtE,6BAA6B;QAC7B,IAAI,CAACE,WAAW,GAAG,IAAIH,QAAiB,CAACC,SAASG;YAChD,IAAI,CAACjC,EAAE,CAAC,UAAU,IAAM8B,QAAQ;YAChC,IAAI,CAAC9B,EAAE,CAAC,OAAO,IAAM8B,QAAQ;YAC7B,IAAI,CAAC9B,EAAE,CAAC,SAAS,CAACkC,MAAQD,OAAOC;QACnC,GAAGC,IAAI,CAAC,CAACC;gBACP,0BAAA;aAAA,2BAAA,CAAA,QAAA,IAAI,EAACL,kBAAkB,qBAAvB,8BAAA;YACA,OAAOK;QACT;QAEA,IAAIf,IAAIgB,SAAS,EAAE;YACjB,IAAI,CAACA,SAAS,GAAGhB,IAAIgB,SAAS;QAChC;IACF;IAEOC,aAAaC,IAAY,EAAEnC,KAAwB,EAAQ;QAChE,MAAMoC,SAASjC,MAAMC,OAAO,CAACJ,SAASA,QAAQ;YAACA;SAAM;QACrD,KAAK,MAAMqC,KAAKD,OAAQ;YACtB,IAAI,CAACrD,OAAO,CAACuD,MAAM,CAACH,MAAME;QAC5B;QAEA,OAAO,IAAI;IACb;IAEA;;;;GAIC,GACD,IAAWE,SAAS;QAClB,OAAO,IAAI,CAACpB,QAAQ,IAAI,IAAI,CAACC,WAAW;IAC1C;IAEA;;;;GAIC,GACD,IAAWb,aAA4B;QACrC,OAAO,IAAI,CAACtB,MAAM;IACpB;IAEOuD,MAAMC,KAAmC,EAAE;QAChD,IAAI,IAAI,CAACR,SAAS,EAAE;YAClB,OAAO,IAAI,CAACA,SAAS,CAACQ;QACxB;QACA,IAAI,CAACpB,OAAO,CAACqB,IAAI,CAACC,OAAOC,QAAQ,CAACH,SAASA,QAAQE,OAAOE,IAAI,CAACJ;QAE/D,OAAO;IACT;IAEOK,MAAM;QACX,IAAI,CAAC3B,QAAQ,GAAG;QAChB,OAAO,KAAK,CAAC2B,OAAOC;IACtB;IAEA;;;;;;GAMC,GACD,AAAOC,kBAAkB,CAAC;IAEnBC,OACLR,KAAsB,EACtBS,SAAiB,EACjBC,QAAoB,EACpB;QACA,IAAI,CAACX,KAAK,CAACC;QAEX,sEAAsE;QACtE,wEAAwE;QACxE,mDAAmD;QACnD,EAAE;QACF,6FAA6F;QAC7FU;IACF;IAWOC,UACL9B,UAAkB,EAClBJ,aAIa,EACbnC,OAAgE,EAC1D;YAqCN,0BAAA;QApCA,IAAI,CAACA,WAAW,OAAOmC,kBAAkB,UAAU;YACjDnC,UAAUmC;QACZ,OAAO,IAAI,OAAOA,kBAAkB,YAAYA,cAAcmC,MAAM,GAAG,GAAG;YACxE,IAAI,CAACnC,aAAa,GAAGA;QACvB;QAEA,IAAInC,SAAS;YACX,qEAAqE;YACrE,mEAAmE;YACnE,2DAA2D;YAC3D,EAAE;YACF,qFAAqF;YACrF,EAAE;YACF,uEAAuE;YACvE,kCAAkC;YAClC,IAAIoB,MAAMC,OAAO,CAACrB,UAAU;gBAC1B,0EAA0E;gBAC1E,oEAAoE;gBACpE,sEAAsE;gBACtE,qDAAqD;gBACrD,IAAK,IAAIuE,IAAI,GAAGA,IAAIvE,QAAQsE,MAAM,EAAEC,KAAK,EAAG;oBAC1C,2DAA2D;oBAC3D,IAAI,CAACC,SAAS,CAACxE,OAAO,CAACuE,EAAE,EAAYvE,OAAO,CAACuE,IAAI,EAAE;gBACrD;YACF,OAAO;gBACL,KAAK,MAAM,CAACvD,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACnB,SAAU;oBAClD,wBAAwB;oBACxB,IAAI,OAAOiB,UAAU,aAAa;oBAElC,IAAI,CAACuD,SAAS,CAACxD,KAAKC;gBACtB;YACF;QACF;QAEA,IAAI,CAACsB,UAAU,GAAGA;QAClB,IAAI,CAACF,WAAW,GAAG;SACnB,2BAAA,CAAA,QAAA,IAAI,EAACO,kBAAkB,qBAAvB,8BAAA;QAEA,OAAO,IAAI;IACb;IAEO6B,UAAUrB,IAAY,EAAW;QACtC,OAAO,IAAI,CAACpD,OAAO,CAAC0E,GAAG,CAACtB;IAC1B;IAEOuB,UAAUvB,IAAY,EAAsB;QACjD,OAAO,IAAI,CAACpD,OAAO,CAACQ,GAAG,CAAC4C,SAASwB;IACnC;IAEOC,aAAkC;QACvC,OAAOlF,0BAA0B,IAAI,CAACK,OAAO;IAC/C;IAEO8E,iBAA2B;QAChC,OAAO1D,MAAM0C,IAAI,CAAC,IAAI,CAAC9D,OAAO,CAAC+E,IAAI;IACrC;IAEOP,UAAUpB,IAAY,EAAEnC,KAAyB,EAAE;QACxD,IAAIG,MAAMC,OAAO,CAACJ,QAAQ;YACxB,qEAAqE;YACrE,uEAAuE;YACvE,IAAI,CAACjB,OAAO,CAACgF,MAAM,CAAC5B;YAEpB,KAAK,MAAME,KAAKrC,MAAO;gBACrB,IAAI,CAACjB,OAAO,CAACuD,MAAM,CAACH,MAAME;YAC5B;QACF,OAAO,IAAI,OAAOrC,UAAU,UAAU;YACpC,IAAI,CAACjB,OAAO,CAACiF,GAAG,CAAC7B,MAAMnC,MAAMiE,QAAQ;QACvC,OAAO;YACL,IAAI,CAAClF,OAAO,CAACiF,GAAG,CAAC7B,MAAMnC;QACzB;QAEA,OAAO,IAAI;IACb;IAEOkE,aAAa/B,IAAY,EAAQ;QACtC,IAAI,CAACpD,OAAO,CAACgF,MAAM,CAAC5B;IACtB;IAEOgC,eAAqB;IAC1B,uEAAuE;IACvE,cAAc;IAChB;IAEA,wEAAwE;IACxE,oBAAoB;IAEpB,IAAWC,sBAA+B;QACxC,MAAM,IAAI1E,MAAM;IAClB;IAEO2E,kBAAkB;QACvB,MAAM,IAAI3E,MAAM;IAClB;IAEA,IAAW4E,MAAuB;QAChC,MAAM,IAAI5E,MAAM;IAClB;IAEO6E,eAAe;QACpB,MAAM,IAAI7E,MAAM;IAClB;IAEO8E,eAAqB;QAC1B,MAAM,IAAI9E,MAAM;IAClB;IAEO+E,gBAAsB;QAC3B,MAAM,IAAI/E,MAAM;IAClB;IAEOgF,kBAAwB;QAC7B,MAAM,IAAIhF,MAAM;IAClB;IAEA,IAAWiF,YAAqB;QAC9B,MAAM,IAAIjF,MAAM;IAClB;IAEA,IAAWkF,kBAA2B;QACpC,MAAM,IAAIlF,MAAM;IAClB;IAEA,IAAWmF,kBAA2B;QACpC,MAAM,IAAInF,MAAM;IAClB;IAEA,IAAWoF,8BAAuC;QAChD,MAAM,IAAIpF,MAAM;IAClB;IAEA,IAAWqF,WAAoB;QAC7B,MAAM,IAAIrF,MAAM;IAClB;IAEOoB,aAAmB;QACxB,MAAM,IAAIpB,MAAM;IAClB;IAEOsF,cAAoB;QACzB,MAAM,IAAItF,MAAM;IAClB;AACF;AAWA,OAAO,SAASuF,2BAA2B,EACzCnG,GAAG,EACHC,UAAU,CAAC,CAAC,EACZC,SAAS,KAAK,EACdW,YAAY,EACZsC,SAAS,EACThD,SAAS,IAAI,EACgB;IAC7B,OAAO;QACLqF,KAAK,IAAI3F,cAAc;YACrBG;YACAC;YACAC;YACAC;YACAC,UAAUS;QACZ;QACAsB,KAAK,IAAIF,eAAe;YAAE9B;YAAQgD;QAAU;IAC9C;AACF"}