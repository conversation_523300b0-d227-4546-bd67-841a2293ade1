import React from 'react'

import Image from 'next/image'

interface LogoProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export default function Logo({ size = 'md', className = '' }: LogoProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-20 h-20'
  }

  return (
    <div className={`${sizeClasses[size]} ${className}`}>
      <Image
        src="/syra-logo.png"
        alt="SYRA Logo"
        width={size === 'sm' ? 32 : size === 'md' ? 40 : 80}
        height={size === 'sm' ? 32 : size === 'md' ? 40 : 80}
        className="w-full h-full object-contain"
      />
    </div>
  )
}
