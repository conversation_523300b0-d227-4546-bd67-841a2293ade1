{"name": "syra-backend", "version": "1.0.0", "description": "SYRA Security Dashboard Backend API", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "node src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["security", "monitoring", "dashboard", "api"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT"}