'use client'

import React, { useState } from 'react'
import { X } from 'lucide-react'
import <PERSON>ton from './Button'

interface AddDomainModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (domain: string) => void
}

export default function AddDomainModal({ isOpen, onClose, onSave }: AddDomainModalProps) {
  const [domain, setDomain] = useState('')

  const handleSave = () => {
    if (domain.trim()) {
      onSave(domain.trim())
      setDomain('')
      onClose()
    }
  }

  const handleCancel = () => {
    setDomain('')
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 shadow-xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Add New Domain</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors p-1"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Domain Link
          </label>
          <input
            type="text"
            value={domain}
            onChange={(e) => setDomain(e.target.value)}
            placeholder="E.g. trenteknologimobile.com"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
          />
        </div>

        {/* Actions */}
        <div className="flex gap-3">
          <Button
            variant="secondary"
            size="md"
            onClick={handleCancel}
            className="flex-1"
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            size="md"
            onClick={handleSave}
            className="flex-1"
          >
            Save
          </Button>
        </div>
      </div>
    </div>
  )
}
