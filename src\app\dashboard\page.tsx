'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'
import But<PERSON> from '@/components/Button'
import AttackStatsCard from '@/components/AttackStatsCard'
import DomainCard from '@/components/DomainCard'
import AddDomainModal from '@/components/AddDomainModal'
import { mockDomains, getTotalAttacksByType } from '@/lib/mockData'

export default function DashboardPage() {
  const router = useRouter()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [domains, setDomains] = useState(mockDomains)

  const attackStats = getTotalAttacksByType()

  const handleLogout = () => {
    router.push('/')
  }

  const handleAddDomain = (domainName: string) => {
    const newDomain = {
      id: String(domains.length + 1),
      name: domainName,
      createdAt: new Date().toLocaleDateString('en-GB', { day: 'numeric', month: 'long', year: 'numeric' }),
      lastUpdate: new Date().toLocaleDateString('en-GB', { day: 'numeric', month: 'long', year: 'numeric' }),
      attackCounts: { ddos: 0, sqlInjection: 0, xss: 0 },
      securityLogs: [
        {
          id: '1',
          timestamp: '[' + new Date().toISOString().slice(0, 16).replace('T', ' ') + ']',
          message: 'Domain monitoring started - no threats detected',
          type: 'normal' as const
        }
      ],
      attacks: [],
      hasActiveAlert: false
    }
    setDomains([...domains, newDomain])
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Navigation onLogout={handleLogout} />
      
      <main className="flex-1 px-6 py-6 max-w-7xl mx-auto w-full">
        {/* Attack Statistics */}
        <section className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Total Number of Attacks Detected
          </h2>
          <div className="flex gap-6 mb-8">
            <AttackStatsCard count={attackStats.ddos} type="DDoS" />
            <AttackStatsCard count={attackStats.sqlInjection} type="SQL Injection" />
            <AttackStatsCard count={attackStats.xss} type="XSS" />
          </div>
        </section>

        {/* My Domains Section */}
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">My Domain</h2>
            <Button
              variant="primary"
              size="md"
              onClick={() => setIsModalOpen(true)}
              className="px-6"
            >
              Add New Domain
            </Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {domains.map((domain) => (
              <DomainCard key={domain.id} domain={domain} />
            ))}
          </div>
        </section>
      </main>

      <Footer />

      <AddDomainModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={handleAddDomain}
      />
    </div>
  )
}
