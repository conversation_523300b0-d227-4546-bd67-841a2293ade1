import React from 'react'
import Logo from './Logo'
import Button from './Button'

interface NavigationProps {
  userName?: string
  onLogout?: () => void
}

export default function Navigation({ userName = "I Nyoman Darmayoga", onLogout }: NavigationProps) {
  return (
    <nav className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        {/* Logo Section */}
        <div className="flex items-center gap-3">
          <Logo size="lg" />
          <h1 className="text-2xl font-bold text-gray-900">SYRA</h1>
        </div>

        {/* User Section */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center">
              <span className="text-white font-semibold text-sm">I</span>
            </div>
            <span className="text-sm font-medium text-gray-900">{userName}</span>
          </div>
          <Button variant="primary" size="sm" onClick={onLogout}>
            Log Out
          </Button>
        </div>
      </div>
    </nav>
  )
}
